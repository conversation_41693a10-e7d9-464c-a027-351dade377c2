The software and materials in this repository are governed by the [NVIDIA Software License Agreement](https://www.nvidia.com/en-us/agreements/enterprise-software/nvidia-software-license-agreement/) and the Product-Specific Terms for [NVIDIA AI Products](https://www.nvidia.com/en-us/agreements/enterprise-software/product-specific-terms-for-ai-products/);  except as follows: (1) models, other than the Cosmos Reason 1-7B model, are governed by the [NVIDIA Community Model License](https://www.nvidia.com/en-us/agreements/enterprise-software/nvidia-community-models-license/), (2) use of the Cosmos Reason 1-7B model is governed by the [NVIDIA Open Models License](https://www.nvidia.com/en-us/agreements/enterprise-software/nvidia-open-model-license/), and (3) mp4 files in /examples/training_notebooks are governed by the (NVIDIA ASSET LICENSE)[https://github.com/NVIDIA-AI-Blueprints/video-search-and-summarization/blob/main/LICENSE.DATA].

ADDITIONAL INFORMATION: [Llama 3.1 Community License Agreement](https://www.llama.com/llama3_1/license/) for Llama-3.1-70b-instruct; [Llama 3.2 Community License Agreement](https://www.llama.com/llama3_2/license/) for NVIDIA Retrieval QA Llama 3.2 1B Embedding v2 and NVIDIA Retrieval QA Llama 3.2 1B Reranking v2; and Apache License, Version 2.0 for Cosmos Reason 1-7B model. Built with Llama.

Some of the portions of this software are provided under the following license.
- Files under "deploy" directory are licensed under [Apache License, Version 2.0](http://www.apache.org/licenses/LICENSE-2.0)
- Files under "examples" directory are licensed under [Apache License, Version 2.0](http://www.apache.org/licenses/LICENSE-2.0)
