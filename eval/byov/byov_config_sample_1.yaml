######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
######################################################################################################

LLM_Judge_Model: gpt-4o #or llama-3.1-70b-instruct


VSS_Configurations:

  - Config_1:
    VLM_Configurations: 
      model: nvila #nvila, vila-1.5, openai-compat, or custom
      model_path: ngc:nvidia/tao/nvila-highres:nvila-lite-15b-highres-lita #model path, using defaults from byov_main.py if not set
      VLM_batch_size:  #only applicable for vila-1.5 and nvila
      frames_per_chunk: #sets to default based on model
      temperature: 0.4
      top_p: 1
      top_k: 100
      max_new_tokens: 100
      seed: 1
      #edit below if using openai-compat
      VIA_VLM_OPENAI_MODEL_DEPLOYMENT_NAME: #for example, "gpt-4o" 
      VIA_VLM_ENDPOINT: #for gpt models, use "https://api.openai.com/v1", otherwise change url to point to remote VLM endpoint. Can be any VLM with an openAI compatible API.
      AZURE_OPENAI_ENDPOINT: #default is None, change url to point to remote VLM endpoint for Azure OpenAI endpoints.
      VLM_INPUT_WIDTH:
      VLM_INPUT_HEIGHT:
    CA_RAG_CONFIG: ca_rag_config.yaml #ca rag config file name (must be in the eval directory)

    Guardrail_Configurations:
      enable: False
      guardrail_config_file: #guardrail config file name (must be in the eval directory)
    
  
videos:
  - video_id: its #id of video
    video_file_name: its.mp4 #name of video file in the media directory
    chunk_size: 10 #chunk size in seconds
    summary_gt: its_ground_truth_summary.json #name of summary ground truth file
    dc_gt: #name of dense caption ground truth file (optional)
    qa_gt: its_ground_truth_qa.json #name of question & answer ground truth file 
    prompts:
      caption: "You are an advanced intelligent traffic monitoring system. You must monitor and take note of all traffic related events like collision, unsafe maneuver, traffic violation, normal traffic flow, obstructed traffic flow etc. with relevant details. Note down all vehicles details in video like vehicle type. Make sure to capture details of emergency responder vehicles like police, ambulance, fire-truck, etc. Don't add vehicle license plate details. Don't add any details which are not present in the video frames. Start each event description with a start and end time stamp of the event."
      caption_summarization: "You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene."
      summary_aggregation: "Based on the available information, generate a traffic report that is organized chronologically and in logical sections. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely. Timestamps are in seconds so please format them as SS.SSS"
    enable_audio: False
    enable_cv: False
  
  
  - video_id: warehouse_82min 
    video_file_name: warehouse_82min.mp4 #name of video file in the media directory
    chunk_size: 60 #chunk size in seconds
    summary_gt: warehouse_82min_ground_truth_summary.json #name of summary ground truth file
    dc_gt: warehouse_82min_ground_truth_dc.json #name of dense caption ground truth file (optional)
    qa_gt: warehouse_82min_ground_truth_qa.json #name of question & answer ground truth file 
    prompts:
      caption: "Write a concise and clear dense caption for the provided warehouse video, focusing on irregular or hazardous events such as boxes falling, workers not wearing PPE, workers falling, workers taking photographs, workers chitchatting, forklift stuck, etc. Start and end each sentence with a time stamp."
      caption_summarization: "You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene."
      summary_aggregation: "Based on the available information, generate a traffic report that is organized chronologically and in logical sections. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely. Timestamps are in seconds so please format them as SS.SSS"
    enable_audio: False
    enable_cv: False
