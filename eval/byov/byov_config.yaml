######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
######################################################################################################

LLM_Judge_Model: gpt-4o #can be any openai model or llama-3.1-70b-instruct


VSS_Configurations:

  - Config_1:
    VLM_Configurations: 
      model:  #nvila, openai-compat, cosmos-reason1, or custom
      model_path:  #model path, using defaults from byov_main.py if not set
      VLM_batch_size: #only applicable for nvila
      frames_per_chunk: #sets to default based on model
      temperature: 0.4
      top_p: 1
      top_k: 100
      max_new_tokens: 100
      seed: 1
      #edit below if using openai-compat
      VIA_VLM_OPENAI_MODEL_DEPLOYMENT_NAME: #for example, "gpt-4o" 
      VIA_VLM_ENDPOINT: #for gpt models, use "https://api.openai.com/v1", otherwise change url to point to remote VLM endpoint. Can be any VLM with an openAI compatible API.
      AZURE_OPENAI_ENDPOINT: #default is None, change url to point to remote VLM endpoint for Azure OpenAI endpoints.

    CA_RAG_CONFIG: ca_rag_config.yaml #ca rag config file name (must be in the eval directory)

    Guardrail_Configurations:
      enable: False
      guardrail_config_file: #guardrail config file name (must be in the eval directory)

videos:
  - video_id: #id of video
    video_file_name: #name of video file in the media directory
    chunk_size: 10 #chunk size in seconds
    summary_gt: #name of summary ground truth file
    dc_gt: #name of dense caption ground truth file (optional)
    qa_gt: #name of question & answer ground truth file (optional)
    prompts:
      caption: 
      caption_summarization: "You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene."
      summary_aggregation: "Based on the available information, generate a traffic report that is organized chronologically and in logical sections. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely. Timestamps are in seconds so please format them as SS.SSS"
    enable_audio: False
    enable_cv: False

  - video_id: #id of video
    video_file_name: #name of video file in the media directory
    chunk_size: 10 #chunk size in seconds
    summary_gt:  #name of summary ground truth file
    dc_gt: #name of dense caption ground truth file (optional)
    qa_gt: #name of question & answer ground truth file (optional)
    prompts:
      caption:
      caption_summarization: "You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene."
      summary_aggregation: "Based on the available information, generate a summary that describes the condition of the bridge. The summary should be organized chronologically and in logical sections. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to understand what happened. Format the output in Markdown so it can be displayed nicely. Timestamps are in seconds so please format them as SS.SSS"
    enable_audio: False
    enable_cv: False
