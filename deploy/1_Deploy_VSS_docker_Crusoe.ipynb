{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# NVIDIA AI Blueprint for Video Search and Summarization: Docker Deployment\n", "\n", "This notebook will go over the steps to deploy the NVIDIA AI Blueprint for Video Search and Summarization which can ingest massive volumes of live or archived videos and extract insights for summarization and interactive Q&A.\n", "\n", "We will go over the docker compose deployment steps which as an alternative to the helm chart deployment. As this blueprint uses multiple models including a VLM, LLM, Reranker, Embedding model, there are multiple ways to deploy the blueprint based on the model deployment (Self-hosted or NV-hosted /w API Key). Here we will be running all models locally/self-hosted.\n", "\n", "**Note**: this notebook is designed to run as a [brev.dev launchable](https://console.brev.dev/launchable/deploy/?launchableID=env-2olGFXbhH0qEtU47MJ4tpxCAZIn) on 8XL40S GPU **(Specifically CRUSOE Cloud Provider with Ephemeral storage)**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites\n", "\n", "### 1. Obtain NVIDIA API Keys\n", "\n", "This key will be used to pull relevant models and containers from build.nvidia.com and NGC.\n", "\n", "Generate the key from [NGC Portal](https://ngc.nvidia.com/). Follow the instructions to [Generate NGC API Key.](https://docs.nvidia.com/ngc/gpu-cloud/ngc-user-guide/index.html#generating-api-key)\n", "\n", "<div class=\"alert alert-block alert-success\">\n", "    <b>Note:</b>  If you have authentication issues when pulling the NIMs, please verify you have the following <a href=\"https://org.ngc.nvidia.com/subscriptions\" target=\"_blank\">Subscriptions</a>: <strong>NVIDIA Developer Program</strong>\n", " </div>\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"NGC_API_KEY\"] = \"***\" #Replace with your key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Specify the Vision Language Model (VLM)\n", "\n", "Here we are configuring the setup to deploy the default Cosmos Reason 1 VLM. Steps to configure a different VLM can be found here: [Configure the VLM](https://docs.nvidia.com/vss/latest/content/installation-vlms-docker-compose.html#). \n", "\n", "Add environments variables accordingly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Cosmos Reason 1\n", "os.environ[\"VLM_MODEL_TO_USE\"] = \"cosmos-reason1\"\n", "# For L40s\n", "os.environ[\"MODEL_PATH\"] = \"git:https://huggingface.co/nvidia/Cosmos-Reason1-7B\"\n", "# For other GPUs\n", "# os.environ[\"MODEL_PATH\"] = \"ngc:nim/nvidia/cosmos-reason1-7b:1.1-fp8-dynamic\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Configure Features\n", "\n", "#### Computer Vision Pipeline\n", "\n", "Set ```DISABLE_CV_PIPELINE``` to ```true``` if you want to disable CV pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## CV Pipeline\n", "\n", "os.environ[\"DISABLE_CV_PIPELINE\"] = \"false\" #Set to true to disable\n", "os.environ[\"NUM_CV_CHUNKS_PER_GPU\"] = \"1\"\n", "os.environ[\"INSTALL_PROPRIETARY_CODECS\"] = \"true\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Audio Transcription\n", "\n", "This uses a ASR NIM, which is deployed in Step 4 of this notebook. To disable audio feature, set the following parameter to ```false```."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"ENABLE_AUDIO\"] = \"true\" #Set to false to disable\n", "\n", "os.environ[\"RIVA_ASR_SERVER_URI\"] = \"host.docker.internal\"\n", "os.environ[\"RIVA_ASR_GRPC_PORT\"] = \"50051\"\n", "os.environ[\"RIVA_ASR_HTTP_PORT\"] = \"9005\"\n", "os.environ[\"RIVA_ASR_SERVER_IS_NIM\"] = \"true\"\n", "os.environ[\"ENABLE_RIVA_SERVER_READINESS_CHECK\"] = \"true\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "#### Ensuring user is on right path\n", "Ignore the warning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%cd ./docker/launchables\n", "!ls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Verify the Driver and CUDA version to be the following:\n", "- Driver Version: 580.65.06\n", "- CUDA Version 13.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!nvidia-smi"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If the driver version doesn't match in the above step:\n", "- Update the Driver to 580\n", "- Reboot the system\n", "- Set ```NGC_API_KEY``` again"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !sudo apt install {nvidia-driver,nvidia-kernel-source,nvidia-kernel-common,nvidia-dkms,nvidia-compute-utils,nvidia-utils,libnvidia-gl,libnvidia-compute,libnvidia-decode,libnvidia-cfg1,libnvidia-extra,libnvidia-encode,libnvidia-fbc1,xserver-xorg-video-nvidia}-580=580.65.06-0ubuntu1 -y\n", "# !sudo reboot now"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deployment: Using all self-hosted models\n", "\n", "We will be using Cosmos Nemotron VLM, which is part of the main container. All other models need to be set up before proceeding with the blueprint container. These include:\n", "- [Embedding NIM](https://build.nvidia.com/nvidia/llama-3_2-nv-embedqa-1b-v2)\n", "- [<PERSON><PERSON><PERSON>](https://build.nvidia.com/nvidia/llama-3_2-nv-rerankqa-1b-v2)\n", "- [LLM NIM](https://build.nvidia.com/meta/llama-3_1-70b-instruct)\n", "- [Riva ASR NIM](https://build.nvidia.com/nvidia/parakeet-ctc-0_6b-asr) [Optional, required to enable audio transcription]\n", "\n", "\n", "### GPU Configuration\n", "\n", "<img src=\"images/vss_gpu_layout_v2.png\" width=\"800\">\n", "\n", "In order to update the GPUs used by each model:\n", "- **LLM, Embedding and Reranking models:** Update the ```--gpus``` parameters while deploying NIMs in Steps 2-4 below.\n", "- **VLM:** Update ```NVIDIA_VISIBLE_DEVICES``` in docker/.env\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Set Environment Variables and Login to <PERSON>er"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#os.environ[\"LOCAL_NIM_CACHE\"] = os.path.expanduser(\"~/.cache/nim\") #default cache location\n", "os.environ[\"LOCAL_NIM_CACHE\"] = os.path.expanduser(\"/ephemeral/cache/nim\") #updating with ephemeral storage\n", "os.makedirs(os.environ[\"LOCAL_NIM_CACHE\"], exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%bash\n", "echo \"${NGC_API_KEY}\" | docker login nvcr.io -u '$oauthtoken' --password-stdin"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Updating the docker storage path to Ephemeral storage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json, subprocess, time\n", "\n", "storage_path = \"/ephemeral/cache/docker\"\n", "\n", "daemon_file = \"/etc/docker/daemon.json\" #update the path if required\n", "config = {}\n", "try:\n", "    config = json.load(open(daemon_file)) if os.path.exists(daemon_file) else {}\n", "except PermissionError:\n", "    print(\"Cannot read the file. Try running with elevated privileges or check docker deamon file path.\")\n", "\n", "config[\"data-root\"] = storage_path\n", "config_str = json.dumps(config, indent=4)\n", "\n", "subprocess.run(f\"echo '{config_str}' | sudo tee {daemon_file} > /dev/null\", shell=True, check=True)\n", "subprocess.run(\"sudo systemctl restart docker\", shell=True, check=True)\n", "\n", "time.sleep(5)\n", "\n", "# Verify new storage location\n", "print(subprocess.run(\"docker info | grep 'Docker Root Dir'\", shell=True, capture_output=True, text=True).stdout)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Launch the LLM NIM.\n", "Note: If you're logged in as root, make a separate llm_user account and give it permission to the nim cache folder.\n", "\n", "Here, we have preset the GPUs to use ```--gpus``` and port ```-p``` for the most optimal deployment on 8xL40s."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker run -it --rm \\\n", "    --gpus '\"device=0,1,2,3\"' \\\n", "    --shm-size=16GB \\\n", "    -e NGC_API_KEY \\\n", "    -v \"$LOCAL_NIM_CACHE:/opt/nim/.cache\" \\\n", "    -u $(id -u) \\\n", "    -p 8000:8000 \\\n", "    -d \\\n", "    nvcr.io/nim/meta/llama-3.1-70b-instruct:1.10.1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3: Launch the Reranker NIM. \n", "\n", "Here we have preset the GPUs to use ```--gpus``` and port ```-p``` for the most optimal deployment on 8xL40s."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker run -it --rm \\\n", "    --gpus '\"device=4\"' \\\n", "    --shm-size=16GB \\\n", "    -e NGC_API_KEY \\\n", "    -v \"$LOCAL_NIM_CACHE:/opt/nim/.cache\" \\\n", "    -u $(id -u) \\\n", "    -p 9235:8000 \\\n", "    -d \\\n", "    nvcr.io/nim/nvidia/llama-3.2-nv-rerankqa-1b-v2:1.7.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 4: Launch the Embedding NIM.\n", "\n", "Again, we have preset the GPUs to use ```--gpus``` and port ```-p``` for the most optimal deployment on 8xL40s."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker run -it --rm \\\n", "    --gpus '\"device=4\"' \\\n", "    --shm-size=16GB \\\n", "    -e NGC_API_KEY \\\n", "    -v \"$LOCAL_NIM_CACHE:/opt/nim/.cache\" \\\n", "    -u $(id -u) \\\n", "    -p 9234:8000 \\\n", "    -d \\\n", "    nvcr.io/nim/nvidia/llama-3.2-nv-embedqa-1b-v2:1.9.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 5: Launch the Riva ASR NIM for Audio Support [Optional, only required for audio capabilities]\n", "\n", "Now we'll add the Riva ASR NIM for audio capabilities. This enables speech-to-text functionality for video summaries. \n", "\n", "Make sure ```ENABLE_AUDIO``` is set to ```true``` in the prerequisites section."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker network create vss_network"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker run -d -it --rm \\\n", "    --name parakeet-ctc-asr \\\n", "    --network vss_network \\\n", "    -p 50051:50051 \\\n", "    -p 9005:9000 \\\n", "    -e NIM_GRPC_API_PORT=50051 \\\n", "    --gpus '\"device=5\"' \\\n", "    --shm-size=16GB \\\n", "    -e NGC_API_KEY \\\n", "    -v \"$LOCAL_NIM_CACHE:/opt/nim/.cache\" \\\n", "    -u $(id -u) \\\n", "    nvcr.io/nim/nvidia/parakeet-0-6b-ctc-en-us:2.0.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 6: Verify all the NIMs are running\n", "\n", "After running the following cell, you should be able to see three containers, one for each model.\n", "\n", "![Container List](images/containers_pre_check_v2.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker ps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Below cell ensures that the LLM NIM is running before proceeding.\n", "\n", "<div class=\"alert alert-block alert-success\">\n", "    <b>Note:</b>  LLM NIM service could take a couple of miniutes to get ready.\n", " </div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = 'http://localhost:8000/v1/health/ready' #make sure the LLM NIM port is correct\n", "headers = {'accept': 'application/json'}\n", "\n", "print(\"Checking LLM NIM readiness...\")\n", "while True:\n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            if data.get(\"message\") == \"Service is ready.\":\n", "                print(\"LLM NIM is ready.\")\n", "                break\n", "            else:\n", "                print(\"LLM NIM is not ready. Waiting for 30 seconds...\")\n", "        else:\n", "            print(f\"Unexpected status code {response.status_code}. Waiting for 30 seconds...\")\n", "    except requests.ConnectionError:\n", "        print(\"LLM NIM is not ready. Waiting for 30 seconds...\")\n", "    time.sleep(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check Riva NIM readiness\n", "riva_url = 'http://localhost:9005/v1/health/ready'\n", "headers = {'accept': 'application/json'}\n", "\n", "print(\"Checking Riva ASR NIM readiness...\")\n", "while True:\n", "    try:\n", "        response = requests.get(riva_url, headers=headers)\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            if data.get(\"status\") == \"ready\":\n", "                print(\"Riva ASR NIM is ready!\")\n", "                break\n", "            else:\n", "                print(\"Riva ASR NIM is not ready. Waiting for 30 seconds...\")\n", "        else:\n", "            print(f\"Unexpected status code {response.status_code}. Waiting for 30 seconds...\")\n", "    except requests.ConnectionError:\n", "        print(\"Riva ASR NIM is not ready. Waiting for 30 seconds...\")\n", "    time.sleep(30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 7: Launch the Blueprint\n", "\n", "Before proceeding, make sure you have compose.yaml, config.yaml and .env in the current directory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls -a"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Update docker compose version (recommended v2.32.4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker compose version\n", "\n", "!mkdir -p ~/.docker/cli-plugins\n", "!curl -SL https://github.com/docker/compose/releases/latest/download/docker-compose-linux-x86_64 -o ~/.docker/cli-plugins/docker-compose\n", "!chmod +x ~/.docker/cli-plugins/docker-compose"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker compose version"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### **Docker Compose Deployment**\n", "\n", "Here, we start the docker container to spin up the blueprint. The container performs the following main steps:\n", "1. Downloads the VLM\n", "2. Performs model calibration\n", "3. Generates a TRT-LLM Engine\n", "4. Spins up the Milvus and Neo4J database.\n", "5. Starts Video Search and Summarization service\n", "6. Finally, we get the frontend and backend endpoints\n", "\n", "<div class=\"alert alert-block alert-success\">\n", "    <b>Note:</b>  This step can take around 30 minutes for the first run as it goes through Steps 1-3 mentioned above\n", " </div>\n", "\n", "The below code cell filters out important logs so that the output is not very verbose"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "!docker compose down"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import time\n", "\n", "keywords = [\"Milvus server started\", \"Downloading model\", \"Downloaded model\", \"Initializing Cosmos-Reason1-7B\", \"Starting to load model\", \n", "            \"Starting quantization\", \"Quantization done\", \"Engine generation completed\", \"TRT engines generated\", \"Uvicorn\", \n", "            \"VIA Server loaded\", \"Backend\", \"Frontend\", \"****\"]\n", "\n", "# Start the docker compose process in detached mode\n", "subprocess.run(['docker', 'compose', 'up', '--quiet-pull', '-d'])\n", "\n", "def filter_logs(logs, keywords):\n", "    return [line for line in logs.splitlines() if any(keyword in line for keyword in keywords)]\n", "\n", "printed_lines = set()\n", "\n", "try:\n", "    while True:\n", "        logs = subprocess.check_output(['docker', 'compose', 'logs', '--no-color'], universal_newlines=True)\n", "        filtered_logs = filter_logs(logs, keywords)\n", "        new_logs = [line for line in filtered_logs if line not in printed_lines]\n", "        \n", "        for line in new_logs:\n", "            print(line)\n", "            printed_lines.add(line)\n", "            if \"Frontend\" in line:\n", "                print(\"VSS Server ran successfully.\")\n", "                print(\"Access VSS Frontend UI from Brev portal tunnels section. Refer to Step 7 for more details.\")\n", "                raise SystemExit\n", "        time.sleep(1)\n", "except KeyboardInterrupt:\n", "    print(\"Stopping log tailing...\")\n", "except SystemExit:\n", "    pass"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 8: Access VSS UI using the Secure Links on Brev\n", "\n", "1. Go to the \"Access\" Tab on Brev and scroll down to the \"Using Secure Links\" section   \n", "    <img src=\"images/brev_access_tab.png\" alt=\"Access Tab\" width=\"1200\"/>\n", "\n", "2. (Optional) Add the VSS frontend port \"9100\" if not already added. This is set in .env file which is configurable   \n", "    <img src=\"images/brev_add_port.png\" alt=\"Access Tab\" width=\"1200\"/>\n", "\n", "3. Click on the frontend port (9100) Sharable URL link to access blueprint UI  \n", "    <div class=\"alert alert-block alert-success\">\n", "        <b>Note:</b>  Please reload the brev page if the shareable URL is showing as unhealthy or follow the alternative steps with VSCode below.\n", "    </div>\n", "    <img src=\"images/brev_vss_ui_9100.png\" alt=\"Access Tab\" width=\"1200\"/>\n", "\n", "4. Experience VSS using the gradio based UI application. For quick steps to summarize a video, refer to [this link](https://docs.nvidia.com/vss/latest/content/sample_summarization.html). Additionally, for detailed instructions on how to use the UI, follow [this guide](https://docs.nvidia.com/vss/latest/content/ui_app.html)  \n", "    <img src=\"images/vss_landing_page.png\" alt=\"Access Tab\" width=\"1200\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### [Alertnative Option] Access instance on VSCode and add ports\n", "\n", "1. First, go to the \"Access\" tab  \n", "    <img src=\"images/brev_access_tab.png\" alt=\"Access Tab\" width=\"1200\"/>\n", "\n", "2. Refer to the commands in \"Using Brev CLI\" to open VSCode locally  \n", "    <div class=\"alert alert-block alert-success\">\n", "        <b>Note:</b>  Make sure to <a href=\"https://code.visualstudio.com/docs/setup/mac#_configure-the-path-with-vs-code\" target=\"_blank\">Configure the path with VS Code</a> if you run into erros while accessing instance through VSCode.\n", "    </div>  \n", "    <img src=\"images/brev_vscode_access.png\" alt=\"Access Tab\" width=\"1200\"/>\n", "\n", "3. Navigate to the Ports view in the Panel region (Ports: Focus on Ports View), and select Forward a Port  \n", "    <img src=\"images/vscode_ports.png\" alt=\"VSCode Ports\" width=\"1200\"/>\n", "\n", "4. Add frontend (9100) and backend (8100) ports, and access blueprint UI by clicking on the \"localhost:9100\"  \n", "    <img src=\"images/vscode_access_vss_ui.png\" alt=\"VSCode Ports\" width=\"1200\"/>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Uninstalling the blueprint\n", "\n", "Uncomment the following cell to stop the blueprint container, followed by stopping all model containers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %%capture\n", "# # To bring down the blueprint instance\n", "# !docker compose down\n", "\n", "# # To stop all other containers\n", "# !docker stop $(docker ps -q)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!docker ps"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 4}