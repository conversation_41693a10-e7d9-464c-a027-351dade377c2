######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
######################################################################################################

models:
  - type: main
    engine: nim
    model: meta/llama-3.1-8b-instruct
    parameters:
      base_url: "http://host.docker.internal:8007/v1"

  - type: embeddings
    engine: nim
    model: nvidia/llama-3.2-nv-embedqa-1b-v2
    parameters:
      base_url: "http://host.docker.internal:8006/v1"

rails:
  input:
    flows:
      - self check input

instructions:
  - type: general
    content: |
      Below is a conversation between a bot and a user about the image or video.
      The bot is factual and concise. If the bot does not know the answer to a
      question, it truthfully says it does not know.

sample_conversation: |
  user "Hello there!"
    express greeting
  bot express greeting
    "Hello! How can I assist you today?"
  user "What can you do for me?"
    ask about capabilities
  bot respond about capabilities
    "I am an AI assistant here to answer questions about the image or video."

# The prompts below are the same as the ones from `nemoguardrails/llm/prompts/llama3.yml`.
prompts:
  - task: general
    messages:
      - type: system
        content: |
          {{ general_instructions }}{% if relevant_chunks != None and relevant_chunks != '' %}
          This is some relevant context:
          ```markdown
          {{ relevant_chunks }}
          ```{% endif %}
      - "{{ history | to_chat_messages }}"

  # Prompt for detecting the user message canonical form.
  - task: generate_user_intent
    messages:
      - type: system
        content: |
          {{ general_instructions }}

          Your task is to generate the user intent in a conversation given the last user message similar to the examples below.
          Do not provide any explanations, just output the user intent.

          # Examples:
          {{ examples | verbose_v1 }}

      - "{{ sample_conversation | first_turns(2) | to_messages }}"
      - "{{ history | colang | to_messages }}"
      - type: assistant
        content: |
            Bot thinking: potential user intents are: {{ potential_user_intents }}

    output_parser: "verbose_v1"

  # Prompt for generating the next steps.
  - task: generate_next_steps
    messages:
      - type: system
        content: |
          {{ general_instructions }}

          Your task is to generate the next steps in a conversation given the last user message similar to the examples below.
          Do not provide any explanations, just output the user intent and the next steps.

          # Examples:
          {{ examples | remove_text_messages | verbose_v1 }}

      - "{{ sample_conversation | first_turns(2) | to_intent_messages }}"
      - "{{ history | colang | to_intent_messages }}"

    output_parser: "verbose_v1"

  # Prompt for generating the bot message from a canonical form.
  - task: generate_bot_message
    messages:
      - type: system
        content: |
            {{ general_instructions }}{% if relevant_chunks != None and relevant_chunks != '' %}
            This is some relevant context:
            ```markdown
            {{ relevant_chunks }}
            ```{% endif %}
            Your task is to generate the bot message in a conversation given the last user message, user intent and bot intent.
            Similar to the examples below.
            Do not provide any explanations, just output the bot message.

            # Examples:
            {{ examples | verbose_v1 }}

      - "{{ sample_conversation | first_turns(2) | to_intent_messages_2 }}"
      - "{{ history | colang | to_intent_messages_2 }}"

    output_parser: "verbose_v1"

# Prompt for generating the value of a context variable.
  - task: generate_value

    messages:
      - type: system
        content: |
          {{ general_instructions }}

          Your task is to generate value for the ${{ var_name }} variable..
          Do not provide any explanations, just output value.

          # Examples:
          {{ examples | verbose_v1 }}

      - "{{ sample_conversation | first_turns(2) | to_messages }}"
      - "{{ history | colang | to_messages }}"
      - type: assistant
        content: |
            Bot thinking: follow the following instructions: {{ instructions }}
            ${{ var_name }} =

    output_parser: "verbose_v1"
