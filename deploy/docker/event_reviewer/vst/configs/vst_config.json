{"network": {"http_port": "30000", "server_domain_name": "", "stunurl_list": ["stun.l.google.com:19302", "stun1.l.google.com:19302"], "static_turnurl_list": [], "use_coturn_auth_secret": false, "coturn_turnurl_list_with_secret": [], "use_twilio_stun_turn": false, "twilio_account_sid": "", "twilio_auth_token": "", "use_reverse_proxy": false, "reverse_proxy_server_address": "REVERSE_PROXY_SERVER_ADDRESS:100", "ntp_servers": [], "use_sensor_ntp_time": false, "max_webrtc_out_connections": 8, "max_webrtc_in_connections": 8, "webservice_access_control_list": "", "rtsp_server_port": 30554, "rtsp_server_instances_count": 8, "rtsp_server_use_socket_poll": true, "rtsp_preferred_network_iface": "", "rtcp_rtp_port_multiplex": true, "rtsp_in_base_udp_port_num": -1, "rtsp_out_base_udp_port_num": -1, "rtsp_streaming_over_tcp": false, "rtsp_server_reclamation_client_timeout_sec": 10, "rx_socket_buffer_size": 1000000, "tx_socket_buffer_size": 1000000, "tx_rtp_packet_size": 1300, "stream_monitor_interval_secs": 5, "rtp_udp_port_range": "31000-31100", "udp_latency_ms": 200, "udp_drop_on_latency": false, "webrtc_latency_ms": 1000, "enable_frame_drop": true, "webrtc_video_quality_tunning": {"resolution_2160": {"bitrate_start": 20000, "bitrate_range": [10000, 50000], "qp_range_I": [0, 20], "qp_range_P": [0, 20]}, "resolution_1440": {"bitrate_start": 10000, "bitrate_range": [5000, 20000], "qp_range_I": [0, 15], "qp_range_P": [0, 10]}, "resolution_1080": {"bitrate_start": 5000, "bitrate_range": [2000, 10000], "qp_range_I": [10, 30], "qp_range_P": [10, 30]}, "resolution_720": {"bitrate_start": 2000, "bitrate_range": [1000, 8000], "qp_range_I": [10, 30], "qp_range_P": [10, 30]}, "resolution_480": {"bitrate_start": 1000, "bitrate_range": [500, 3000], "qp_range_I": [10, 30], "qp_range_P": [10, 30]}}, "webrtc_peer_conn_timeout_sec": 10, "enable_grpc": false, "grpc_server_port": "50051", "webrtc_in_audio_sender_max_bitrate": 128000, "webrtc_in_video_degradation_preference": "detail", "webrtc_in_video_sender_max_framerate": 30, "remote_vst_address": "", "webrtc_port_range": {"min": 30001, "max": 30100}, "enable_websocket_pingpong": false, "websocket_keep_alive_ms": 5000, "tokkio_plugin_server_url": ""}, "onvif": {"device_discovery_timeout_secs": 10, "onvif_request_timeout_secs": 10, "device_discovery_freq_secs": 5, "device_discovery_interfaces": [], "max_devices_supported": 500, "default_bitrate_kbps": 8000, "default_framerate": 30, "default_resolution": "1920x1080", "default_gov_length": 60, "onvif_sensor_time_sync_interval_secs": 60, "onvif_sensor_time_sync_compensation_ms": 20}, "data": {"storage_config_file": "./configs/vst_storage.json", "storage_threshold_percentage": 95, "storage_monitoring_frequency_secs": 2, "enable_cloud_storage": false, "cloud_storage_type": "minio", "cloud_storage_endpoint": "http://127.0.0.1:9000", "cloud_storage_access_key": "admin", "cloud_storage_secret_key": "nvidia123!", "cloud_storage_bucket": "videos", "cloud_storage_region": "", "cloud_storage_use_ssl": false, "nv_streamer_directory_path": "/home/<USER>/vst_release/streamer_videos/", "nv_streamer_loop_playback": true, "nv_streamer_seekable": false, "nv_streamer_sync_playback": false, "nv_streamer_sync_file_count": 0, "nv_streamer_max_upload_file_size_MB": 10000, "nv_streamer_media_container_supported": ["mp4", "mkv"], "nv_streamer_metadata_container_supported": ["json"], "nv_streamer_rtsp_server_output_buffer_size_kb": 1000, "supported_video_codecs": ["h264", "h265"], "supported_audio_codecs": ["pcmu", "pcma", "mpeg4-generic"], "enable_aging_policy": false, "max_video_download_size_MB": 1000, "always_recording": true, "event_recording": false, "event_record_length_secs": 10, "record_buffer_length_secs": 2, "use_software_path": false, "use_webrtc_inbuilt_encoder": "", "webrtc_in_fixed_resolution": "1280x720", "webrtc_in_max_framerate": 30, "webrtc_in_video_bitrate_thresold_percentage": 50, "webrtc_in_passthrough": false, "webrtc_sender_quality": "pass_through", "enable_rtsp_server_sei_metadata": false, "enable_proxy_server_sei_metadata": false, "gpu_indices": [], "webrtc_out_enable_insert_sps_pps": true, "webrtc_out_set_iframe_interval": 30, "webrtc_out_set_idr_interval": 256, "webrtc_out_min_drc_interval": 5, "webrtc_out_encode_fallback_option": "software", "device_name": "VST", "device_location": "", "enable_dec_low_latency_mode": true, "enable_avsync_udp_input": true, "use_standalone_udp_input": false, "enable_silent_audio_in_udp_input": false, "enable_udp_input_dump": false, "webrtc_out_default_resolution": "1920x1080", "use_webrtc_hw_dec": true, "recorder_enable_frame_drop": true, "recorder_max_frame_queue_size_bytes": 16000000, "webrtc_out_enc_quality_tuning": "ultra_low_latency", "webrtc_out_enc_preset": "ultra_fast", "enable_drc": true, "enable_ipc_path": false, "ipc_socket_path": "/tmp/", "ipc_src_buffer_timestamp_copy": true, "ipc_src_connection_attempts": 5, "ipc_src_connection_interval_us": 1000000, "ipc_sink_buffer_timestamp_copy": true, "ipc_sink_buffer_copy": true, "use_external_peerconnection": false, "use_stream_sdk": false, "enable_mega_simulation": false, "mega_simulation_delay_min_ms": 0, "mega_simulation_delay_max_ms": 5000, "mega_simulation_base_time": "2024-09-27T10:00:00.123456Z", "max_network_db_connections": 10, "need_file_to_rtsp": false}, "notifications": {"enable_notification": true, "enable_notification_consumer": false, "use_message_broker": "redis", "use_message_broker_consumer": "kafka", "message_broker_topic": "vst_events", "message_broker_topic_consumer": "", "message_broker_payload_key": "sensor.id", "message_broker_metadata_topic": "test", "redis_server_env_var": "", "kafka_server_address": ""}, "debug": {"enable_perf_logging": true, "enable_qos_monitoring": true, "qos_logfile_path": "", "qos_data_capture_interval_sec": 1, "qos_data_publish_interval_sec": 5, "enable_gst_debug_probes": true, "enable_prometheus": false, "prometheus_port": "8080", "enable_system_metric": false, "system_metric_interval_sec": 30, "enable_highlighting_logs": true, "enable_debug_apis": true, "dump_webrtc_input_stats": false, "enable_frameid_in_webrtc_stream": false, "enable_network_bandwidth_notification": false, "enable_latency_logging": true, "enable_loopback_multicast": false}, "overlay": {"video_metadata_server": "", "video_metadata_query_batch_size_num_frames": 300, "use_video_metadata_protobuf": true, "enable_gem_drawing": true, "analytic_server_address": "", "calibration_file_path": "/home/<USER>/vst_release/configs/calibration.json", "3d_overlay_sensor_name": "", "calibration_mode": "synthetic", "use_camera_groups": true, "enable_recentering": true, "overlay_text_font_type": "DejaVuSansMono.ttf", "floor_map_file_path": "/home/<USER>/vst_release/configs/gev.png", "bbox_tolerance_ms": 33, "overlay_color_code": [{"Person": [118, 185, 0, 255]}, {"Agility_Digit_Humanoid": [0, 113, 197, 255]}, {"Fourier_GR1_T2_Humanoid": [0, 113, 197, 255]}, {"NovaCarter": [250, 194, 0, 255]}, {"Transporter": [0, 133, 100, 255]}, {"Forklift": [0, 113, 197, 255]}, {"Box": [250, 194, 0, 255]}, {"Pallet": [93, 22, 130, 255]}, {"Crate": [94, 94, 94, 255]}, {"proximity_bubble": [0, 255, 0, 75]}, {"proximity_bubble_inner": [255, 0, 0, 75]}, {"proximity_bubble_outer": [204, 85, 0, 75]}, {"proximity_bubble_border": [255, 255, 255, 255]}, {"proximity_line": [255, 255, 255, 255]}]}, "security": {"use_https": false, "use_rtsp_authentication": false, "use_http_digest_authentication": false, "use_multi_user": false, "enable_user_cleanup": false, "session_max_age_sec": 2592000, "multi_user_extra_options": ["Secure", "SameSite=none"], "nv_org_id": "", "nv_ngc_key": ""}}