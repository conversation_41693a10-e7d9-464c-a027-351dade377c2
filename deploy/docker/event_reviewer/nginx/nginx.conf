# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


events {
    worker_connections 1024;
}

http {
    error_log /var/log/nginx/error.log debug;
    log_format custom '$remote_addr - $remote_user [$time_local] '
                      '"$request" $status $body_bytes_sent '
                      '"$http_referer" "$http_user_agent"';
    access_log /var/log/nginx/access.log custom;
    # vss Server (Backend API)
    upstream vss_server_backend {
        server via-server:8000;
    }

    # Storage Management Service
    upstream storage_ms {
        server storage-ms:30000;  # Default STORAGE_HTTP_PORT
    }

    # Alert Bridge Service
    upstream alert_bridge {
        server alert-bridge:9080;
    }

    # Alert Inspector UI (Gradio) - Commented out since service is disabled
    upstream alert_inspector_ui_gradio {
        server alert-inspector-ui:7860;
    }

    server {
        listen 80;
        server_name localhost; # Use localhost for local testing

        # vss Server Backend API
        location /api/vss/ {
            proxy_pass http://vss_server_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Storage Management Service
        location /api/storage/ {
            proxy_pass http://storage_ms/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            #proxy_set_header X-Forwarded-Proto $scheme;
        }


        # Alert Bridge Service
        location /api/alertbridge/ {
            proxy_pass http://alert_bridge/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Alert Inspector UI - Gradio Interface (Commented out since service is disabled)
        location /alertinspector/ {
            proxy_pass http://alert_inspector_ui_gradio/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint
        location /health {
            return 200 "API Gateway healthy\n";
            add_header Content-Type text/plain;
        }

        # Root redirect to health check since alert-inspector is disabled
        location / {
            return 301 /health;
        }
    }
}