######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
######################################################################################################
---
# Environment configuration

tools:
  graph_db:
    type: neo4j
    params:
      host: !ENV ${GRAPH_DB_HOST}
      port: !ENV ${GRAPH_DB_BOLT_PORT}
      username: !ENV ${GRAPH_DB_USERNAME}
      password: !ENV ${GRAPH_DB_PASSWORD}
    tools:
      embedding: nvidia_embedding

  vector_db:
    type: milvus
    params:
      host: !ENV ${MILVUS_DB_HOST}
      port: !ENV ${MILVUS_DB_GRPC_PORT}
    tools:
      embedding: nvidia_embedding

  chat_llm:
    type: llm
    params:
      model: meta/llama-3.1-70b-instruct
      base_url: "http://host.docker.internal:8000/v1" #FIXME - update url to running LLM Instance
      max_tokens: 2048
      temperature: 0.2
      top_p: 0.7

  summarization_llm:
    type: llm
    params:
      model: meta/llama-3.1-70b-instruct
      base_url: "http://host.docker.internal:8000/v1" #FIXME - update url to running LLM Instance
      max_tokens: 2048
      temperature: 0.2
      top_p: 0.7

  notification_llm:
    type: llm
    params:
      model: meta/llama-3.1-70b-instruct
      base_url: "http://host.docker.internal:8000/v1" #FIXME - update url to running LLM Instance
      max_tokens: 2048
      temperature: 0.2
      top_p: 0.7

  nvidia_embedding:
    type: embedding
    params:
      model: nvidia/llama-3.2-nv-embedqa-1b-v2
      base_url: "http://host.docker.internal:9234/v1" #FIXME

  nvidia_reranker:
    type: reranker
    params:
      model: nvidia/llama-3.2-nv-rerankqa-1b-v2
      base_url: "http://host.docker.internal:9235/v1" #FIXME - update url to running reranker NIM Instance

  notification_tool:
    type: alert_sse_notifier
    params:
      endpoint: "http://127.0.0.1:60000/via-alert-callback"

functions:
  summarization:
    type: batch_summarization
    params:
      batch_size: 6
      batch_max_concurrency: 20
      prompts:
        caption: "Write a concise and clear dense caption for the provided warehouse video, focusing on irregular or hazardous events such as boxes falling, workers not wearing PPE, workers falling, workers taking photographs, workers chitchatting, forklift stuck, etc. Start and end each sentence with a time stamp."
        caption_summarization: "You should summarize the following events of a warehouse in the format start_time:end_time:caption. For start_time and end_time use . to seperate seconds, minutes, hours. If during a time segment only regular activities happen, then ignore them, else note any irregular activities in detail. The output should be bullet points in the format start_time:end_time: detailed_event_description. Don't return anything else except the bullet points."
        summary_aggregation: "You are a warehouse monitoring system. Given the caption in the form start_time:end_time: caption, Aggregate the following captions in the format start_time:end_time:event_description. If the event_description is the same as another event_description, aggregate the captions in the format start_time1:end_time1,...,start_timek:end_timek:event_description. If any two adjacent end_time1 and start_time2 is within a few tenths of a second, merge the captions in the format start_time1:end_time2. The output should only contain bullet points.  Cluster the output into Unsafe Behavior, Operational Inefficiencies, Potential Equipment Damage and Unauthorized Personnel"
    tools:
      llm: summarization_llm
      db: graph_db

  ingestion_function:
    type: graph_ingestion
    params:
      batch_size: 1
      image: false
      cot: false
      top_k: 5
    tools:
      llm: chat_llm
      db: graph_db

  retriever_function:
    type: graph_retrieval
    params:
      batch_size: 1
      image: false
      cot: false
      top_k: 5
    tools:
      llm: chat_llm
      db: graph_db

  notification:
    type: notification
    params:
      events: []
    tools:
      llm: notification_llm
      notification_tool: notification_tool

context_manager:
  functions:
    - summarization
    - ingestion_function
    - retriever_function
    - notification
