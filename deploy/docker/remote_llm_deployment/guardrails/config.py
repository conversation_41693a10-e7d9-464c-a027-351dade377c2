######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
######################################################################################################


from functools import lru_cache
from typing import List, Optional

from langchain_nvidia_ai_endpoints import ChatNVIDIA, Model, register_model
from nemoguardrails import LLMRails
from nemoguardrails.embeddings.providers.base import EmbeddingModel
from nemoguardrails.embeddings.providers.registry import EmbeddingProviderRegistry
from nemoguardrails.llm.providers import register_llm_provider


class NIMEmbeddingModelPatch(EmbeddingModel):
    """Embedding model using langchain-nvidia-ai-endpoints.

    This class is a wrapper for using embedding models powered by NIM
    (hosted in the NVIDIA API Catalog or locally).

    Args:
        embedding_model (str): The name embedding model to be used.

    Attributes:
        model: The name of the model to be called for creating embeddings.
        embedding_size: The dimensionality of the embeddings generated by the model.
    """

    engine_name = "nim"

    def __init__(self, embedding_model: str, base_url: Optional[str] = None):
        try:
            from langchain_nvidia_ai_endpoints import NVIDIAEmbeddings

            self.model = embedding_model
            self.document_embedder = NVIDIAEmbeddings(model=embedding_model, base_url=base_url)

        except ImportError:
            raise ImportError(
                "Could not import langchain_nvidia_ai_endpoints, please install it with "
                "`pip install langchain-nvidia-ai-endpoints`."
            )

    async def encode_async(self, documents: List[str]) -> List[List[float]]:
        """Encode a list of documents into their corresponding sentence embeddings.

        Args:
            documents (List[str]): The list of documents to be encoded.

        Returns:
            List[List[float]]: The list of sentence embeddings,
            where each embedding is a list of floats.
        """

        result = await self.document_embedder.aembed_documents(documents)
        return result

    def encode(self, documents: List[str]) -> List[List[float]]:
        """Encode a list of documents into their corresponding sentence embeddings.

        Args:
            documents (List[str]): The list of documents to be encoded.

        Returns:
            List[List[float]]: The list of sentence embeddings,
            where each embedding is a list of floats.
        """
        return self.document_embedder.embed_documents(documents)


def _get_embedding_model_parameters(config):
    """Return the parameters of models of type 'embeddings'.

    Args:
        config (Config): The configuration object containing model information.

    Returns:
        list: A list of parameters for models of type 'embeddings'.
    """
    embedding_parameters = []
    for model in config.models:
        if model.type == "embeddings":
            embedding_parameters.append(model.parameters)
    return embedding_parameters


def init(app: LLMRails):
    embedding_parameters = _get_embedding_model_parameters(app.config)

    if not embedding_parameters:
        raise ValueError("No embedding model parameters found in the configuration.")

    base_url = embedding_parameters[0].get("base_url", None)

    if not base_url:
        raise ValueError("base_url not found in the embedding model parameters.")

    # Dynamically create a subclass with base_url being fixed, Python needs partial for classes :D
    class FixedNIMEmbeddingModel(NIMEmbeddingModelPatch):
        def __init__(self, embedding_model: str):
            super().__init__(embedding_model, base_url=base_url)

    FixedNIMEmbeddingModel.__name__ = "FixedNIMEmbeddingModel"

    if "nim_patch" not in EmbeddingProviderRegistry():
        app.register_embedding_provider(FixedNIMEmbeddingModel, "nim_patch")
