######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
######################################################################################################

define user express greeting
  "Hello!"
  "Hi"
  "Wassup?"

define bot express greeting
  "Hello!"

define bot ask how are you
  "How are you doing?"

define flow greeting
  user express greeting
  bot express greeting
  bot ask how are you

define flow self check input
  $allowed = execute self_check_input

  if not $allowed
    bot refuse to respond
    stop

define bot calls lmm
  "lmm"

define user ask about politics
  "What do you think about the government?"
  "Which party should I vote for?"

define user ask about stock market
  "Which stock should I invest in?"
  "Would this stock 10x over the next year?"
  "Tell me why Nvidia stock is going up?"

define flow politics
  user ask about politics
  bot refuse to respond

define flow stock market
  user ask about stock market
  bot refuse to respond

define flow general question
  user ask general question
  bot refuse to respond

define flow lmm
  user ask about video image content
  bot calls lmm

define user ask about video image content
  "You are a video summarization system that will be given sequential clips of a video captured from a traffic camera at an intersection. The output should be bullet points in the format start_time:end_time: detailed_event_description.  Be sure to note all traffic related events and activity of all vehicles."
  "Summarize the captions"
  "is there a worker?"
  "is there a forklift?"
  "did any collison happen?"
  "Were there any car crashes or vehicle collisions?"
  "What time is the red ambulance present in the video?"
  "What time did the event occur?"
  "What scene had the most animals?"
  "What animal was the cheetah chasing?"
  "How long were the zebras running in the video?"
  "Is the top part of the bridge rusted?"
  "Describe the surroundings of the bridge."
  "Where is the rust most concentrated?"
  "How many cones do you see?"

define user ask general question
  "What stocks should I buy?"
  "Can you recommend the best stocks to buy?"
  "Can you recommend a place to eat?"
  "Do you know any restaurants?"
  "Can you tell me your name?"
  "What's your name?"
  "Can you paint?"
  "Can you tell me a joke?"
  "What is the biggest city in the world"
  "Can you write an email?"
  "I need you to write an email for me."
  "Who is the president?"
  "What party will win the elections?"
  "Who should I vote with?"

define bot refuse to respond
  "I'm sorry, I can't respond to that."

define user ask about capabilities
  "What can you do for me?"
  "How can you help me?"

define bot respond about capabilities
  "I am an AI assistant here to answer questions about the image or video. Please let me know what you need help with, and I will do my best to assist you."

define flow user ask capabilities
  user ask about capabilities
  bot respond about capabilities


