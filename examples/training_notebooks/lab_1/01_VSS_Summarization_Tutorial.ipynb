{"cells": [{"cell_type": "markdown", "id": "d5d501ed-c430-4483-9498-7eb9b6882734", "metadata": {}, "source": ["SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", "\n", "SPDX-License-Identifier: Apache-2.0"]}, {"cell_type": "markdown", "id": "c5d6f756-a7f2-4454-bd75-f64d1c139815", "metadata": {}, "source": ["# Part 1: Understanding Videos using Metropolis Video Search and Summarization AI Blueprint (VSS)\n", "\n", "## Summarization and Vector-RAG\n", "\n", "In this notebook we'll explore the [NVIDIA AI blueprint for video search and summarization (VSS)](https://build.nvidia.com/nvidia/video-search-and-summarization/blueprintcard) which can be used to generate insights for input videos. Here we will be using a zero-shot foundation model. However, fine-tuned models can be integrated with VSS to enable specific tasks.\n", "\n", "### Learning Objectives:\n", "This notebook explores the following topics:\n", "* VSS REST APIs for file management and summarization\n", "* Implementation of summarization using Vector-RAG\n", "* VSS configuration options through the REST APIs for specific summarization use cases \n"]}, {"cell_type": "markdown", "id": "edaa1077-48e0-47d9-adf5-2da611fd3a1c", "metadata": {}, "source": ["### Table of Contents\n", "\n", "**[Set Up the Environment](#Set-Up-the-Environment)**  \n", "**[VSS API Overview](#VSS-API-Overview)**  \n", "**[Video Summarization](#Video-Summarization)**  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[File Uploading](#File-Uploading)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Summarize](#Summarize)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Summarization Pipeline and CA-RAG](#Summarization-Pipeline-and-CA-RAG)  \n", "\n", "**[Summarization Configurations - Intelligent Traffic System](#Summarization-Configurations---Intelligent-Traffic-System)**  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Prompts](#Prompts)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Chunk Duration](#Chunk-Duration)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Model Parameters](#Model-Parameters)  \n", "\n", "**[Review](#Review)**"]}, {"cell_type": "markdown", "id": "9b7179e5-ec02-4b8b-bfb4-53c3d4dd69d8", "metadata": {}, "source": ["<img src=\"assets/vss_arch_diagram.png\" alt=\"vss arch diagram\" width=1000>"]}, {"cell_type": "markdown", "id": "fac4f060-4b61-4fb1-829a-102d440b22d3", "metadata": {}, "source": ["---\n", "### Part 0: Set Up the Environment"]}, {"cell_type": "markdown", "id": "3e11be6e-b00b-44ea-bb8d-ee0e3288f9e9", "metadata": {}, "source": ["Make sure you have a VSS instance running before proceeding. If needed, adjust the vss_url parameter to point to your VSS instance. \n", "\n", "There are also two sample videos should in the images folder:\n", "1) A video of a warehouse\n", "2) A video of a synthetically generated traffic intersection"]}, {"cell_type": "code", "execution_count": null, "id": "4390f2f2-1fd8-48f0-bd1c-f09e72314199", "metadata": {}, "outputs": [], "source": ["vss_url = \"http://localhost:8100\"\n", "warehouse_video = \"assets/warehouse.mp4\"\n", "traffic_video = \"assets/traffic.mp4\""]}, {"cell_type": "markdown", "id": "453e3a7b", "metadata": {}, "source": [" The next cell will install all the necessary Python packages for this notebook"]}, {"cell_type": "code", "execution_count": null, "id": "bb214af6", "metadata": {}, "outputs": [], "source": ["import sys \n", "python_exe = sys.executable\n", "!{python_exe} -m pip install -r requirements.txt "]}, {"cell_type": "markdown", "id": "9a6b2fa3-fce2-441a-8a81-5e00f08f77b7", "metadata": {}, "source": ["Let's also import the following libraries."]}, {"cell_type": "code", "execution_count": null, "id": "c3bbcd06-e575-4e81-8922-f6300e759408", "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "from IPython.display import Markdown, Video, display\n", "import time"]}, {"cell_type": "markdown", "id": "e6ea2bfa-9b60-4ed7-ac8e-83dfdc13ab32", "metadata": {}, "source": ["---\n", "### Part 1: VSS API Overview\n", "\n", "VSS provides REST API endpoints that are used to interact with the blueprint. These endpoints are the integration point of VSS into your own application or service. \n", "\n", "The APIs include the following \n", "- <PERSON><PERSON><PERSON>\n", "- Files\n", "- Health Check\n", "- Live Stream\n", "- Metrics\n", "- Models\n", "- Recommended Configs\n", "- Summarization and Q&A\n", "\n", "You can also refer to the [API Glossary in the documentation](https://docs.nvidia.com/vss/latest/content/API_doc.html#vss-api-glossary) for more details on how to use each API.\n", "\n", "<img src=\"assets/swagger_docs.png\" alt=\"VSS Swagger\" width=1000>"]}, {"cell_type": "markdown", "id": "6f76131b-e461-4b3e-90fc-fcfa28aa399a", "metadata": {}, "source": ["For the REST API endpoints, a 200 status code is returned on success and the response is in JSON format. The following helper function is defined to verify the request responses and help debug if any errors occur. "]}, {"cell_type": "code", "execution_count": null, "id": "d88ba786-f0a4-4b84-89ba-00b94bf2bb6c", "metadata": {}, "outputs": [], "source": ["#helper function to verify responses \n", "def check_response(response, text=False):\n", "    print(f\"Response Code: {response.status_code}\")\n", "    if response.status_code == 200:\n", "        print(\"Response Status: Success\")\n", "        if text:\n", "            print(response.text)\n", "            return response.text\n", "        else:\n", "            print(json.dumps(response.json(), indent=4))\n", "            return response.json()\n", "    else:\n", "        print(\"Response Status: Error\")\n", "        print(response.text)\n", "        return None "]}, {"cell_type": "markdown", "id": "6d971f5d-aabb-4af1-a6aa-f1bb610c0429", "metadata": {}, "source": ["We will be exploring the endpoints below:"]}, {"cell_type": "code", "execution_count": null, "id": "0665deed-7995-4c3b-a034-e5b5d9e68477", "metadata": {}, "outputs": [], "source": ["files_endpoint = vss_url + \"/files\" #upload and manage files\n", "summarize_endpoint = vss_url + \"/summarize\" #summarize uploaded content \n", "health_endpoint = vss_url + \"/health/ready\" #check the status of the VSS server\n", "models_endpoint = vss_url + \"/models\" #view the configured model in VSS"]}, {"cell_type": "markdown", "id": "52d65a13-0ee7-4bc6-89e2-f30ca06ae552", "metadata": {}, "source": ["Let's use the health endpoint to verify your VSS instance is running. It should return a 200 status code. \n", "\n", "<div class=\"alert alert-block alert-info\">\n", "        <b>Note:</b>  It takes about 10 minutes once the server has started for VSS to boot up as it download and sets up required models. Make sure the following cell outputs \"Response Code: 200\" before proceeding.\n", "    </div>"]}, {"cell_type": "code", "execution_count": null, "id": "a9f9c630-ead1-4ed1-ae8e-74ea62cc7882", "metadata": {}, "outputs": [], "source": ["try:\n", "    resp = requests.get(vss_url + \"/health/ready\")\n", "    resp = check_response(resp, text=True)\n", "except Exception as e:\n", "    print(f'Server not ready: {e}')"]}, {"cell_type": "markdown", "id": "4ef861c7-c28c-4cfd-9242-0f8e3dc63ee7", "metadata": {}, "source": ["The models endpoint will return the VLM available to use for summarization requests. This is based on the the startup configuration for VSS. This VLM could be configured to use one of the tightly integrated VLMs (VILA-1.5, NVILA) or a VLM with an OpenAI compatible REST API interface like GPT-4o. "]}, {"cell_type": "code", "execution_count": null, "id": "d89dd018-7178-4d32-bab6-f99c03ef9720", "metadata": {}, "outputs": [], "source": ["try:\n", "    resp = requests.get(vss_url + \"/models\")\n", "    resp = check_response(resp)\n", "    configured_vlm = resp[\"data\"][0][\"id\"]\n", "except Exception as e:\n", "    print(f'Server not ready: {e}')"]}, {"cell_type": "code", "execution_count": null, "id": "de908bda-8d32-4ba3-a433-77b70a020dc0", "metadata": {}, "outputs": [], "source": ["print(f\"Configured VLM: {configured_vlm}\")"]}, {"cell_type": "markdown", "id": "13ae12af-0771-4196-b4a7-79ae2cd329fc", "metadata": {}, "source": ["---\n", "### Part 2: Video Summarization \n", "\n", "This section will show how to upload a video file and make a request to VSS to produce a simple summary over a synthetically generated two minute video of a traffic intersection."]}, {"cell_type": "code", "execution_count": null, "id": "df7fca61-a825-4183-a456-3f8e1a355e73", "metadata": {}, "outputs": [], "source": ["Video(traffic_video, width=1000, embed=True)"]}, {"cell_type": "markdown", "id": "8eee5a5f-fb78-4583-8d8d-a9caf80cb5d5", "metadata": {}, "source": ["---\n", "#### 2.1: File Uploading\n", "\n", "The first step to summarize a video with VSS is to upload a video file through the REST APIs. Several endpoints are available to interact with the files. \n", "\n", "<img src=\"assets/file_endpoints.png\" alt=\"file endpoints\" width=1000>\n"]}, {"cell_type": "markdown", "id": "613f4cd5-ad5f-4d9f-a247-fa779e779c4f", "metadata": {}, "source": ["To send a request with the video file, it should be opened with \"rb\" to get the binary content of the file. Then, we can add it as a file in the body of the request. The request should also specify the ```purpose``` as \"vision\" and ```media_type``` as \"video\". A single image could also be uploaded with media_type \"image\" to summarize a single image file. \n", "\n", "This request can then be posted to the ```/files``` endpoint as a multipart form. "]}, {"cell_type": "code", "execution_count": null, "id": "5d699756-fbbb-44b1-9000-98e51f28e0c4", "metadata": {}, "outputs": [], "source": ["with open(traffic_video, \"rb\") as file:\n", "    files = {\"file\": (\"traffic_video\", file)} #provide the file content along with a file name \n", "    data = {\"purpose\":\"vision\", \"media_type\":\"video\"}\n", "    response = requests.post(files_endpoint, data=data, files=files) #post file upload request \n", "response = check_response(response)\n", "video_id = response[\"id\"] #save file ID for summarization request"]}, {"cell_type": "markdown", "id": "6332d72c-2bbd-4396-bb46-e8e71f434a02", "metadata": {}, "source": ["Once posted, a unique ID will be returned that is used to reference this uploaded file in the summarization request. "]}, {"cell_type": "markdown", "id": "4a6b7003-55af-42eb-80c8-04413df9d150", "metadata": {}, "source": ["To view all the uploaded files, send a get request to the ```/files``` endpoint. "]}, {"cell_type": "code", "execution_count": null, "id": "47f2d31b-1cd2-4cbd-ae44-139debeefb5c", "metadata": {}, "outputs": [], "source": ["resp = requests.get(files_endpoint, params={\"purpose\":\"vision\"})\n", "resp = check_response(resp)"]}, {"cell_type": "markdown", "id": "9a9d40dd-5d8a-4d5f-93e7-0f06138f69f0", "metadata": {}, "source": ["---\n", "#### 2.2: Summarize"]}, {"cell_type": "markdown", "id": "ac79b968-1e34-4eb4-ab12-b1071c1c8b89", "metadata": {}, "source": ["Once a video or image has been uploaded, the ```/summarize``` endpoint can be called to generate a summary. \n", "\n", "<img src=\"assets/summarize_endpoint.png\" alt=\"summarize endpoint\" width=1000>\n"]}, {"cell_type": "markdown", "id": "2bfbb128-0ea1-443d-bd80-de92488da9a3", "metadata": {}, "source": ["In the body of the request, the video ID should be included along with the prompt and model options. We will explore all these options later in the notebook."]}, {"cell_type": "code", "execution_count": null, "id": "f5d881e0-548c-44e6-82f6-7a822d0fe1b7", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id, #id of file returned after upload \n", "    \"prompt\": \"Write a caption based on the video clip.\",\n", "    \"caption_summarization_prompt\": \"Combine sequential captions to create more concise descriptions.\",\n", "    \"summary_aggregation_prompt\": \"Write a summary of the video. \",\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.8,\n", "    \"top_p\": 0.8,\n", "    \"chunk_duration\": 5,\n", "    \"chunk_overlap_duration\": 0\n", "}"]}, {"cell_type": "markdown", "id": "ccedd2b7-b898-471c-8bf1-0a5399a20292", "metadata": {}, "source": ["The body can then be posted to the ```/summarize``` endpoint to start the summarization process. \n", "<div class=\"alert alert-block alert-info\">\n", "        <b>Note:</b>  Depending on the video length and configuration options this request will take some time to return. \n", "    </div>"]}, {"cell_type": "code", "execution_count": null, "id": "5f93e03d-a71c-4882-9698-aef4f651041b", "metadata": {}, "outputs": [], "source": ["response = requests.post(summarize_endpoint, json=body)\n", "response = check_response(response)\n", "generic_summary = response[\"choices\"][0][\"message\"][\"content\"]\n", "summary_id = response[\"id\"] #save to inspect later"]}, {"cell_type": "markdown", "id": "8d559fda-4a9b-42ba-b5a2-ae2edb9456bb", "metadata": {}, "source": ["The request response includes the summary output, some metadata and a unique request ID. The summary is returned in a format similar to the OpenAI API specification. You can extract the summary from the first message in the choices list: ```response[\"choices\"][0][\"message\"][\"content\"]```. Run the next cell to render the summary output in the notebook. "]}, {"cell_type": "code", "execution_count": null, "id": "7ae6aa49-be0b-47f5-905f-01598ad58c06", "metadata": {}, "outputs": [], "source": ["display(Markdown(\"### Summary Output\")) \n", "markdown_string = \"\\n\".join(f\"> {line}\" for line in generic_summary.splitlines())\n", "display(Markdown(markdown_string)) #render summary output as markdown"]}, {"cell_type": "markdown", "id": "2cd984e2-ab40-4a84-b71f-85f3800b7d2a", "metadata": {}, "source": ["The summary output is very short and does not capture many details or any timestamp information. This is becuase the parameters provided in the summarization request were very generic. To improve the response, the prompt, caption_summarization_prompt, summary_aggregation_prompt, chunk duration, chunk_overlap_duration, temperature and top_p can all be tuned to improve the output. To understand how to best configure the summarization request, we need to dive into how the summarization pipeline works. "]}, {"cell_type": "markdown", "id": "79cb78c6-06f9-4bea-8021-dafbd5c6f3d1", "metadata": {}, "source": ["---\n", "\n", "####  2.3: Summarization Pipeline and CA-RAG\n", "\n", "Summarization is a multi-stage pipeline that involves a series of VLM and LLM calls to understand the contents of the input video. This pipeline is GPU accelerated and can run with optimized VLM and LLM NIMs. To produce an informative summary, the LLM is augmented with details of the video generated by the VLM which along with a vector database makes up the Context Aware RAG module of VSS. \n", "\n", "When a summarization request is posted, the input video is first split into many smaller segments or 'chunks'. The size of these chunks is configured by the ```chunk_duration``` parameter. Typical sizes for a chunk are between 10 and 60 seconds. \n", "\n", "Each video chunk is processed in parallel by a VLM. The VLM will inspect a chunk of video by taking in a few frames sampled from the video segment and then produce a text description of what occurred in the chunk. The text description output of the VLM can be influenced by the ```prompt``` parameter. \n", "\n", "<img alt=\"summarization diagram\" src=\"assets/summarization_diagram.png\" width=1000>\n", "\n", "\n", "A batch of VLM dense captions is then provided to a LLM along with the 'caption_summarization_prompt' to condense the captions and reduce any repetitive information. The diagram shows this step with a batch size of 2. The batch size is configurable in the CA-RAG configuration yaml file when VSS is launched. \n", "\n", "The final step in summarization is a single LLM call that takes in the condensed captions and produces the final summary output. The generation of this summary is controlled by the ```summary_aggregation_prompt``` parameter. "]}, {"cell_type": "markdown", "id": "04070402-0770-4a22-a603-0ef37cf5e829", "metadata": {}, "source": ["---\n", "### Part 3: Summarization Configurations - Intelligent Traffic System\n", "\n", "This section will walk through the available configuration options and how they can be tuned to turn VSS into an intelligent traffic system capable of producing traffic reports. \n", "\n", "Several options are available to tune the summary output. The most important are the prompts supplied to the VLM and LLM. \n", "These prompts can be supplied through the configuration file as a defaults or given directly to the summarize endpoint making the request. \n", "\n", "<img alt=\"summarization prompts\" src=\"assets/summarization_prompts_diagram.png\" width=1000>"]}, {"cell_type": "markdown", "id": "a8ebba9b-3da3-481e-b6a6-1570ec41d072", "metadata": {}, "source": ["#### 3.1: Prompts"]}, {"cell_type": "markdown", "id": "8e569a79-0d5f-47c9-81c1-b3e5ea94e13d", "metadata": {}, "source": ["A set of three prompts are used to control the summary generation through the three stages shown in the diagram. The following sections will show how to improve upon the generic prompts used earlier to produce a more informative summary for warehouse related use cases. "]}, {"cell_type": "markdown", "id": "52657ed1-4f44-4340-a6d5-d9403b713ef1", "metadata": {}, "source": ["##### Prompt (VLM)\n", "\n", "The VLM prompt must include enough information so the model knows what it should be looking for in the video. If the summary is missing important details it is likely becuase the VLM did not extract those details from the video chunks in the first place. \n", "\n", "Often what works well is a three part prompt: \n", "\n", "1) Persona \n", "2) Details\n", "3) Format\n", "\n", "For example: \n", "\n", "> \"You are an intelligent traffic system. You must monitor and take note of all traffic related events. Start each event description with a start and end time stamp.\"\n", "\n", "\n", "Giving the VLM the persona of an intelligent traffic system orients its reponses to include relevant details needed to generate a traffic report. We can then add in the prompt any specific details it should be looking for such as traffic events. Finally we often want the summary report to include timestamp information so we must tell the VLM to include the time stamp in the descriptions. \n", "\n", "When VSS chunks the video and provides sampled frames from a chunk to the VLM, it also includes timestamps so the model knows where in the video each frame occured. The model can then use this timestamp information in the output to correlate when events occured in the video. \n", "\n", "With this more specific prompt, the VLM will produce more detailed captions with relevant information which is critical to getting a good summary. "]}, {"cell_type": "code", "execution_count": null, "id": "b0cd34cb-ce67-4689-afe0-8e80a2c8c7a6", "metadata": {}, "outputs": [], "source": ["prompt = \"You are an intelligent traffic system. You must monitor and take note of all traffic related events. Start each event description with a start and end time stamp.\""]}, {"cell_type": "markdown", "id": "043c51b1-0fec-4c13-a5b6-34b9991e50bc", "metadata": {}, "source": ["##### Caption Summarization Prompt (LLM) \n", "\n", "Often, the text descriptions generated by the VLM can be repetetitive in sequential or overlaping chunks. For very long videos, this can be a waste of tokens when producing the final summary. To condense the VLM captions an LLM is used to combine the VLM outputs to produce a more concise description over a batch of chunks.\n", "\n", "This prompt can typically stay the same across use cases as it just needs to instruct the LLM to combine similar descriptions together. \n", "\n", "For example:\n", ">\"You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene\""]}, {"cell_type": "code", "execution_count": null, "id": "a62c235c-7c23-4e77-9cc2-41800f658f12", "metadata": {}, "outputs": [], "source": ["caption_summarization_prompt = \"You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene\""]}, {"cell_type": "markdown", "id": "6de0dbd5-e556-41da-b06b-196c24704233", "metadata": {}, "source": ["##### Summary Aggregation Prompt (LLM)\n", "\n", "The summary aggregation prompt is used to generate the final summary returned by summarization endpoint. It is used in a single LLM call along with all the aggregated captions to generate the summary output. \n", "\n", "This prompt should reiterate what details need to be included in the summary and any formatting options. Keep in mind that at this stage, the summary can only include details that are present in aggregated captions produced in the previous stage.\n", "\n", "For example:\n", ">\"Based on the available information, generate a traffic report that is organized chronologically and in logical sections.Give each section a descriptive heading of what occurs and the time range. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely.\""]}, {"cell_type": "code", "execution_count": null, "id": "b705d9f1-2696-413a-b700-fe363c8c4e5d", "metadata": {}, "outputs": [], "source": ["summary_aggregation_prompt = \"Based on the available information, generate a traffic report that is organized chronologically and in logical sections.Give each section a descriptive heading of what occurs and the time range. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely.\""]}, {"cell_type": "code", "execution_count": null, "id": "351092ae-d885-4a84-a16f-07c7e2ad827f", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompt,\n", "    \"caption_summarization_prompt\": caption_summarization_prompt,\n", "    \"summary_aggregation_prompt\": summary_aggregation_prompt,\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.2,\n", "    \"top_p\": 0.8,\n", "    \"chunk_duration\": 20,\n", "    \"chunk_overlap_duration\": 0\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=body)\n", "response = check_response(response)\n", "summary = response[\"choices\"][0][\"message\"][\"content\"]\n"]}, {"cell_type": "markdown", "id": "eb535c45-ca20-4af6-ace8-59afdc53c631", "metadata": {}, "source": ["Run the following cells to render the summary outputs of the generic prompts and the tuned prompts side by side. "]}, {"cell_type": "code", "execution_count": null, "id": "a5df3a09-c2c7-4444-b772-317d9a1e5b33", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "  <h1> Generic Prompts </h1>\n", "    {generic_summary}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "  <h1> Tuned Prompts </h1>\n", "    \\n{summary}\n", "  </div>\n", "</div>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "95f8489c-fe91-47cf-bbff-f7b715f8c69c", "metadata": {}, "outputs": [], "source": ["Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "12923948-1df4-4dbf-b717-d929f0d689db", "metadata": {}, "source": ["The summary output with the tuned prompts should be signficantly more informative compared to the summary generated with the generic prompts. It should include much more detailed information related to the traffic video, timestamps of relevant events and be in an easy to ready format. "]}, {"cell_type": "markdown", "id": "0d60c3ca-4825-4a7a-90f1-2640e519b0b8", "metadata": {}, "source": ["---\n", "#### 3.2: <PERSON><PERSON>"]}, {"attachments": {}, "cell_type": "markdown", "id": "80aa32c5-e19c-4e03-976c-07aea6b9bcf4", "metadata": {}, "source": ["In addition to the prompts, the ```chunk_duration``` (also known as chunk size) is very important to tune based on the use case. The chunk size determines the temporal granularity at which the VLM will view the video.\n", "\n", "<img alt=\"chunk duration\" src=\"assets/chunk_duration.png\" width=1000>\n", "\n", "The number of chunks and frames processed by the VLM can be calculated with the following: \n", "\n", "$ Number\\ of\\ Chunks = \\frac{Video\\ Length\\ (s)}{Chunk\\ Size\\ (s)} $  <!-- Display-style math -->  \n", "$ Processed\\ Frames = Frames\\ per\\ Chunk * Number\\ of\\ Chunks $  <!-- Display-style math -->  \n", "$ Processed\\ Frames = Frames\\ per\\ Chunk * \\frac{Video\\ Length (s)}{Chunk\\ Size (s)}  $  <!-- Display-style math -->  \n", "\n", "\n", "Now lets plug in some real numbers. \n", "\n", "Video Length = 2 minutes (120 seconds)   \n", "<PERSON><PERSON> Size = 5 seconds   \n", "Frames per Chunk = 10 (default value in VSS)   \n", "\n", "$ Number\\ of\\ Chunks = \\frac{120}{5} = 24\\ Chunks $  <!-- Display-style math -->  \n", "$ Processed\\ Frames = 10 * 24 = 240\\ Frames  $  <!-- Display-style math -->  \n", "\n", "\n", "If the chunk size is adjusted to 30 seconds: \n", "\n", "$ Number\\ of\\ Chunks = \\frac{120}{30} = 4\\ Chunks $  <!-- Display-style math -->  \n", "$ Processed\\ Frames = 10 * 4 = 40\\ Frames  $  <!-- Display-style math -->  \n", "\n", "\n", "\n", "Based on the formula, summarization with a lower value for the chunk size, results in more frames from the video being processed and used in summary generation. A higher value chunk sizes will result in fewer frames being used to generate the summary. \n", "\n", "With fewer frames (longer chunk size), the summary generation will be faster however, finer details or quick events may be missed because the VLM did not see as many frames from the video. \n", "\n", "With a larger number of frames (shorter chunk size), the summary generation will be slower but will include more details and have a higher likelihood of catching quick details and events such as a worker dropping the box. \n", "\n", "The optimal chunk size depends on the use cases and must be tuned to find the right balance between processing time and temporal resolution of the summary. \n", "\n", "Additionally, a ```chunk_overlap_duration``` can also be added to the summarization request to configure overlap between chunks. This can help capture events that may occur at chunk boundaries. \n", "\n", "The following cells will produce a side-by-side comparison of a summary with a chunk size of 30 compared to a chunk size of 5. "]}, {"cell_type": "code", "execution_count": null, "id": "eb52d140-378a-492a-b7a5-9c51ad6f3bf8", "metadata": {}, "outputs": [], "source": ["prompt = \"You are an intelligent traffic monitoring system that will be given a clip from a camera overlooking a four way intersection. You must inspect the clip and write a detailed description of what occurs. End each sentence with a timestamp.\"\n", "caption_summarization_prompt = \"If any descriptions have the same meaning and are sequential then combine them under one sentence and merge the time stamps to a range. Format the timestamps as 'mm:ss'\"\n", "summary_aggregation_prompt = \"Write out a detailed time line based on the descriptions. The output should be a bulleted list in the format 'mm:ss-mm:ss Description' that includes the timestamp and description of what occured.\""]}, {"cell_type": "code", "execution_count": null, "id": "e12e2e73-b8b7-4f00-9cc7-c68185588796", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompt,\n", "    \"caption_summarization_prompt\": caption_summarization_prompt,\n", "    \"summary_aggregation_prompt\": summary_aggregation_prompt,\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.2,\n", "    \"top_p\": 0.8,\n", "    \"chunk_duration\": 30,\n", "    \"chunk_overlap_duration\": 5\n", "}\n", "\n", "start_t = time.time() \n", "response = requests.post(summarize_endpoint, json=body)\n", "summary_30_time = time.time() - start_t \n", "response = check_response(response)\n", "summary_30 = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c44d0cc6-a5d2-4299-8686-1ca02404b6a1", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompt,\n", "    \"caption_summarization_prompt\": caption_summarization_prompt,\n", "    \"summary_aggregation_prompt\": summary_aggregation_prompt,\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.2,\n", "    \"top_p\": 0.8,\n", "    \"chunk_duration\": 5,\n", "    \"chunk_overlap_duration\": 1\n", "}\n", "\n", "start_t = time.time() \n", "response = requests.post(summarize_endpoint, json=body)\n", "summary_5_time = time.time() - start_t\n", "response = check_response(response)\n", "summary_5 = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "4aa18f9b-2fe6-4972-ae99-e40cb7333190", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "  <h1> <PERSON><PERSON> Si<PERSON>: 30 seconds </h1>\n", "  <h1> Generation time: {round(summary_30_time, 2)} seconds </h1>\n", "    \\n{summary_30}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "  <h1> <PERSON><PERSON> Size: 5 seconds </h1>\n", "  <h1> Generation time: {round(summary_5_time, 2)} seconds </h1>\n", "    \\n{summary_5}\n", "  </div>\n", "</div>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4479a682-efbb-479d-a30c-4c1eb1bbcf5e", "metadata": {}, "outputs": [], "source": ["Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "d897632f-7548-4123-b674-8ccba726790c", "metadata": {}, "source": ["From the two summaries, the one produced with a chunk size of 5 seconds should have many more details and finer grain timestamp information however the generation time is higher."]}, {"cell_type": "markdown", "id": "e60913f5-7faa-4246-8385-294b14a27180", "metadata": {}, "source": ["---\n", "#### 3.3: Model Parameters"]}, {"cell_type": "markdown", "id": "39a80276-3b99-4e26-beb3-0dfd67deea0a", "metadata": {}, "source": ["The summarize API also accepts parameters to control the LLM during summary generation. The important ones to note are:\n", "- max_tokens \n", "- temperature \n", "- top_p \n", "\n", "The ```max_tokens``` parameter controls the maximum length of summary generation. If the summary generation is longer than the max_tokens then it will be cut off. If you find the summary is getting cutoff then increase the max tokens. If the summary is too verbose, then reduce the max tokens. \n", "\n", "The ```temperature``` and ```top_p``` parameters influence the probabilities when choosing the next output token. Higher temperature means there is a more equal likelihood between all tokens. A high top_p allows for more variety of tokens to get chosen. This variety can be a good thing when being used to come up with new ideas, creative writing or getting varied outputs. However, it can also lead to outputs with hallucinations. \n", "\n", "For use cases where repeatability and low chance of hallucinations are important, a low ```temperature``` and low ```top_p``` should be used.   \n", "\n", "The following cells will compare summaries with high and low values of ```temperature``` and ```top_p```. "]}, {"cell_type": "code", "execution_count": null, "id": "48eedaab-0439-4f31-a1e0-2a8ef715ff3f", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompt,\n", "    \"caption_summarization_prompt\": caption_summarization_prompt,\n", "    \"summary_aggregation_prompt\": summary_aggregation_prompt,\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.9,\n", "    \"top_p\": 0.9,\n", "    \"chunk_duration\": 20\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=body)\n", "response = check_response(response)\n", "summary_high_t = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "2ef16956-17c8-4d79-a7da-46f4ef332b2b", "metadata": {}, "outputs": [], "source": ["body = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompt,\n", "    \"caption_summarization_prompt\": caption_summarization_prompt,\n", "    \"summary_aggregation_prompt\": summary_aggregation_prompt,\n", "    \"model\": configured_vlm,\n", "    \"max_tokens\": 1024,\n", "    \"temperature\": 0.1,\n", "    \"top_p\": 0.1,\n", "    \"chunk_duration\": 20\n", "} \n", "response = requests.post(summarize_endpoint, json=body)\n", "response = check_response(response)\n", "summary_low_t = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "5de9af1d-7171-4e3a-a458-e25b3a53fe3c", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "  <h1> Low Temperature, Low Top P</h1>\n", "    \\n{summary_low_t}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "  <h1> High Temperature, High Top P </h1>\n", "    \\n{summary_high_t}\n", "  </div>\n", "</div>\n", "\"\"\"\n", "Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "a3f8ab1c-0f2e-49c8-b5d0-8cece2bf9c78", "metadata": {}, "source": ["There may be little difference in the quality of the outputs when adjusting `temperature` and `top_p`. Inspect the side-by-side comparison above and see if you can spot any differences in the output. Generally, a value of 0.2 for both `temperature` and `top_p` will provide good quality outputs for summarization use cases. In practice, the prompts and chunk size will have a larger effect on the quality of output compared to `temperature` and `top_p`. "]}, {"cell_type": "markdown", "id": "166c597d-acee-4685-898f-648b3dbcdc11", "metadata": {}, "source": ["---\n", "### Review\n", "\n", "In this notebook you learned the following:\n", "- How to use VSS to summarize a video\n", "- How to adjust prompts to affect summarization quality\n", "- How to adjust parameters to affect summarization quality"]}, {"cell_type": "markdown", "id": "574a1cc7-c6cd-4f4a-b582-3239458d498d", "metadata": {}, "source": ["In Lab 2, we'll kick things up a notch by implementing a question-and-answer system."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}