{"cells": [{"cell_type": "markdown", "id": "5d44a863-a557-4b02-ba90-3d7fc448a78c", "metadata": {}, "source": ["SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.\n", "\n", "SPDX-License-Identifier: Apache-2.0"]}, {"cell_type": "markdown", "id": "f24546b1-5b35-42f7-bbb2-5bc2d18170bc", "metadata": {}, "source": ["# Part 2: Deploying Metropolis VSS for Search and Summarization\n", "\n", "## Q&A and Graph-RAG\n", "\n", "In the last notebook, we learned how to use the [Blueprint for video search and summarization](https://build.nvidia.com/nvidia/video-search-and-summarization/blueprintcard) to summarize a video.\n", "\n", "In this notebook, we will explore additional features of the VSS blueprint, specifically the Q&A using Graph-RAG functionalities. We will demonstrate these features on warehouse videos to illustrate how VSS can be applied in real-world scenarios.\n", "\n", "### Learning Objectives:\n", "The goals of this notebook are to:\n", "- Explore Q&A on videos through VSS REST APIs\n", "- Understand Graph-RAG Components\n", "- Visualize knowledge graph in Neo4J"]}, {"cell_type": "markdown", "id": "74a48b5a-bc78-4890-810c-cf4a67e6cd3d", "metadata": {}, "source": ["### Table of Contents\n", "\n", "**[Set Up the Environment](#Set-Up-the-Environment)**  \n", "**[Exploring Q&A on Videos](#Exploring-Q&A-on-Videos)**  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Upload Video File](#Upload-Video-File)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Ingest and Process Video](#Ingest-and-Process-Video)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Ask Questions](#Ask-Questions)  \n", "\n", "**[Understanding Graph-RAG Components](#Understanding-Graph-RAG-Components)**  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[3.1 G-Extraction/Indexing](#3.1-G-Extraction/Indexing)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[G-Retriever](#G-Retriever)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[G-Generation](#G-Generation)  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Let's Try a Few More Questions](#Let's-Try-a-Few-More-Questions)  \n", "\n", "**[Graph-RAG Visualization](#Graph-RAG-Visualization)**  \n", "&nbsp;&nbsp;&nbsp;&nbsp;[Cypher Queries](#Cypher-Queries)  \n", "\n", "**[Review](#Review)**"]}, {"cell_type": "markdown", "id": "15677ac4-e38b-4f1d-897d-1ed540471c12", "metadata": {}, "source": ["#### Q&A with VSS\n", "\n", "VSS supports Question-Answering (Q&A) functionality via **Vector-RAG** and **Graph-RAG**. Vector-RAG is the only supported method for live stream processing. And Graph-RAG is specifically designed for video-based queries.\n", "\n", "**Q&A with Vector-RAG:** Captions generated by the VLM, along with their embeddings, are stored in Milvus DB. Given a query, the top five most relevant chunks are retrieved, re-ranked using ```llama-3.2-nv-rerankqa-1b-v2```, and passed to a LLM to generate the final answer.\n", "\n", "**Q&A with Graph-RAG:** To capture the complex information produced by the VLM, a knowledge graph is built and stored during video ingestion. Use an LLM to convert the dense captions in a set of nodes, edges, and associated properties. This knowledge graph is stored in a graph database. Captions and embeddings, generated with ```llama-3.2-nv-embedqa-1b-v2```, are also linked to these entities. By using Graph-RAG techniques, an LLM can access this information to extract key insights for Q&A.\n", "\n", "<img alt=\"VSS CA-RAG Diagram\" src=\"assets/VSS_CA-RAG.png\" width=1000>"]}, {"cell_type": "markdown", "id": "33824692-b1a3-4929-af28-dae42b159aa7", "metadata": {}, "source": ["---\n", "### Part 0: Set Up the Environment"]}, {"cell_type": "markdown", "id": "00972be0-d94a-4d53-b216-91cb4a04f3b5", "metadata": {}, "source": ["We will be using the same VSS server as the previous notebook. Let's verify that it is up and running."]}, {"cell_type": "code", "execution_count": null, "id": "b165e1d3-82c1-47a5-8a40-28cd22eefb50", "metadata": {}, "outputs": [], "source": ["vss_url = \"http://localhost:8100\"\n", "\n", "warehouse_video = \"assets/warehouse.mp4\"\n", "keynote_video = \"assets/keynote_clip.mp4\"\n", "traffic_video = \"assets/traffic.mp4\""]}, {"cell_type": "code", "execution_count": 7, "id": "5921808a-2750-4a2d-9514-22abe2c3c083", "metadata": {}, "outputs": [], "source": ["health_endpoint = vss_url + \"/health/ready\" #check the status of the VSS server\n", "upload_file_endpoint = vss_url + \"/files\" #upload and manage files\n", "summarize_endpoint = vss_url + \"/summarize\" #summarize uploaded content\n", "qna_endpoint = vss_url + \"/chat/completions\" #ask questions for ingested video"]}, {"cell_type": "markdown", "id": "fe24a603", "metadata": {}, "source": [" The next cell will install all the necessary Python packages for this notebook"]}, {"cell_type": "code", "execution_count": null, "id": "cdc6e5b2", "metadata": {}, "outputs": [], "source": ["import sys \n", "python_exe = sys.executable\n", "!{python_exe} -m pip install -r requirements.txt "]}, {"cell_type": "code", "execution_count": 9, "id": "3ad262f9-0b9a-463c-b43e-1f2941739ccd", "metadata": {}, "outputs": [], "source": ["#helper function to verify responses \n", "import json\n", "import requests\n", "from IPython.display import Markdown, display\n", "\n", "def check_response(response, text=False):\n", "    print(f\"Response Code: {response.status_code}\")\n", "    if response.status_code == 200:\n", "        print(\"Response Status: Success\")\n", "        if text:\n", "            print(response.text)\n", "            return response.text\n", "        else:\n", "            print(json.dumps(response.json(), indent=4))\n", "            return response.json()\n", "    else:\n", "        print(\"Response Status: Error\")\n", "        print(response.text)\n", "        return None "]}, {"cell_type": "markdown", "id": "3b1d652c-f426-4604-93b6-222961c3285b", "metadata": {}, "source": ["Let's use the health endpoint to verify your VSS instance is running. **Make sure the following cell outputs \"Response Code: 200\" before proceeding.**"]}, {"cell_type": "code", "execution_count": 10, "id": "232c9b29-4d94-42fb-a5e5-3617ab72f35e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response Code: 200\n", "Response Status: Success\n", "\n"]}], "source": ["resp = requests.get(health_endpoint)\n", "resp = check_response(resp, text=True)"]}, {"cell_type": "markdown", "id": "559d5550-8c53-48ae-9be7-f0a072d9c838", "metadata": {}, "source": ["Then lets save the configured VLM model so we can use it in future requests. "]}, {"cell_type": "code", "execution_count": null, "id": "84c491e3-9658-42da-9b77-ca41d864c575", "metadata": {}, "outputs": [], "source": ["try:\n", "    resp = requests.get(vss_url + \"/models\")\n", "    resp = check_response(resp)\n", "    configured_vlm = resp[\"data\"][0][\"id\"]\n", "except Exception as e:\n", "    print(f'Server not ready: {e}')"]}, {"cell_type": "code", "execution_count": null, "id": "e5e3fb9b-9249-40fc-9fc9-e58c528947e4", "metadata": {}, "outputs": [], "source": ["print(f\"Configured VLM: {configured_vlm}\")"]}, {"cell_type": "markdown", "id": "74cfaed7-c3ed-48ca-9ad6-ee16bd0f1523", "metadata": {}, "source": ["---\n", "### Part 1: Exploring Q&A on Videos\n", "\n", "Please refer to the previous lab for exploring all REST API endpoints. Below we will use REST APIs to upload a file, start video processing with chat enabled, and then try out a few questions.\n", "\n", "<!-- ![Warehouse Scene](images/warehouse.png) -->\n", "\n", "<video width=\"1000 \" height=\" \" \n", "       src=\"assets/warehouse.mp4\"  \n", "       controls>\n", "</video>"]}, {"cell_type": "markdown", "id": "fbb732aa-237a-470b-9173-1e44d7a1ef45", "metadata": {}, "source": ["---\n", "#### 1.1: Upload Video File\n", "\n", "Let's start by uploading a video and storing the file-id from the response."]}, {"cell_type": "code", "execution_count": null, "id": "af0ec3dc-f14d-4610-ae30-05c7d8fe8ee1", "metadata": {}, "outputs": [], "source": ["with open(warehouse_video, \"rb\") as file:\n", "    files = {\"file\": (\"warehouse_video\", file)} #provide the file content along with a file name \n", "    data = {\"purpose\":\"vision\", \"media_type\":\"video\"}\n", "    response = requests.post(upload_file_endpoint, data=data, files=files) #post file upload request \n", "response = check_response(response)\n", "video_id = response[\"id\"] #save file ID for summarization request"]}, {"cell_type": "markdown", "id": "7a73edf3-dc30-4e41-acda-7b77fee69a88", "metadata": {}, "source": ["To view all the uploaded files, send a get request to the ```/files``` endpoint. "]}, {"cell_type": "code", "execution_count": null, "id": "996ca359-915e-4ee0-861e-fa63a33a4a58", "metadata": {}, "outputs": [], "source": ["resp = requests.get(upload_file_endpoint, params={\"purpose\":\"vision\"})\n", "resp = check_response(resp)"]}, {"cell_type": "markdown", "id": "25111831-4cf8-4560-b246-583204690d87", "metadata": {}, "source": ["---\n", "#### 1.2 Ingest and Process Video\n", "\n", "Next, let's process the video to generate dense captions and knowledge graph. This step can take a couple of minutes.\n", "- First, we'll set the prompts\n", "- Then, we'll call the summarize API to ingest the video\n", "- Note that we set ```enable_chat``` to True to create the knowledge graph"]}, {"cell_type": "code", "execution_count": 15, "id": "88d2eb82-1733-4346-be59-53a904370853", "metadata": {}, "outputs": [], "source": ["prompts = {\n", "    \"vlm_prompt\": \"You are a warehouse monitoring system. Describe the events in this warehouse and look for any anomalies. \"\n", "                            \"Start each sentence with start and end timestamp of the event.\",\n", "    \n", "    \"caption_summarization\": \"You will be given captions from sequential clips of a video. Aggregate captions in the format \"\n", "                             \"start_time:end_time:caption based on whether captions are related to one another or create a continuous scene.\",\n", "    \n", "    \"summary_aggregation\": \"Based on the available information, generate a summary that captures the important events in the video. \"\n", "                           \"The summary should be organized chronologically and in logical sections. This should be a concise, \"\n", "                           \"yet descriptive summary of all the important events. The format should be intuitive and easy for a \"\n", "                           \"user to read and understand what happened. Format the output in Markdown so it can be displayed nicely.\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "541af40f-d3f5-4e3b-8ed4-16dace493663", "metadata": {}, "outputs": [], "source": ["summarize_payload = {\n", "    \"id\": video_id,\n", "    \"prompt\": prompts['vlm_prompt'],\n", "    \"caption_summarization_prompt\": prompts['caption_summarization'],\n", "    \"summary_aggregation_prompt\": prompts['summary_aggregation'],\n", "    \"model\": configured_vlm,\n", "    \"chunk_duration\": 10,\n", "    \"chunk_overlap_duration\": 0,\n", "    \"summarize\": True,  #processes the video, but doesn't generate a summary\n", "    \"enable_chat\": True  #enables knowledge graph creation\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=summarize_payload)\n", "response = check_response(response)"]}, {"cell_type": "markdown", "id": "c61f53b6-2df1-4123-9e7d-4592e90cb84d", "metadata": {}, "source": ["---\n", "#### 1.3: Ask Questions\n", "\n", "Once the video is processed, the ```/chat/completions``` endpoint can be called to ask a question\n", "\n", "<img alt=\"Q&A endpoint\" src=\"assets/qna_swagger.png\" width=1000>"]}, {"cell_type": "code", "execution_count": 17, "id": "dbba17fa-d36b-4021-9c97-76b7fe5a36d2", "metadata": {}, "outputs": [], "source": ["#helper function to ask question for a specific video\n", "def qna(query, video_id=video_id):\n", "    print(video_id)\n", "\n", "    payload = {\n", "        \"id\": video_id,\n", "        \"messages\": [{\"content\": query, \"role\": \"user\"}],\n", "        \"model\": configured_vlm\n", "    }\n", "\n", "    try:\n", "        response = requests.post(qna_endpoint, json=payload)\n", "        if response.status_code == 200:\n", "            response_data = response.json()\n", "            # Extracting the answer content\n", "            answer = response_data.get(\"choices\", [])[0].get(\"message\", {}).get(\"content\", \"\")\n", "            return answer if answer else \"No answer received.\"\n", "        else:\n", "            return f\"Failed to get a response. Status code: {response.status_code}. Response: {response.text}\"\n", "    \n", "    except requests.RequestException as e:\n", "        return f\"An error occurred: {e}\""]}, {"cell_type": "code", "execution_count": null, "id": "6c4bd72b-aced-4a91-8a13-2e31458711c0", "metadata": {}, "outputs": [], "source": ["qna(\"Was there any forklift in the scene?\")"]}, {"cell_type": "code", "execution_count": null, "id": "d7fe94fe-46fb-4a4d-9e98-86278e719e87", "metadata": {}, "outputs": [], "source": ["qna(\"Was the worker carrying the box wearing PPE?\")"]}, {"cell_type": "markdown", "id": "cbb99f91-1e42-4be6-8de0-ba97358ff02f", "metadata": {}, "source": ["You will be able to try more questions later in the notebook"]}, {"cell_type": "markdown", "id": "c6e529e6-27f1-43ea-93f2-66e5ef9e10d1", "metadata": {}, "source": ["---\n", "### Part 2: Understanding Graph-RAG Components\n", "\n", "<img alt=\"GraphRAG Diagram\" src=\"assets/GraphRAG.png\" width=800>"]}, {"cell_type": "markdown", "id": "29824c1a-336d-4374-a4fc-d136672889f7", "metadata": {}, "source": ["---\n", "#### Graph-Extraction/Indexing\n", "\n", "##### Dense Captions to Graph Conversion:\n", "The Graph Extractor uses an LLM to analyze dense captions or any text input and identify key entities, actions, and relationships within the text.\n", "\n", "##### Example:\n", "Given a warehouse video scene caption like:  \n", "*\"A worker places a heavy box on the conveyor belt, and the box falls due to improper placement.\"*\n", "\n", "- The LLM can extract entities such as:\n", "  - **Worker** (Person)\n", "  - **Box** (Object)\n", "  - **Conveyor Belt** (Equipment)\n", "\n", "- Relationships identified might include:\n", "  - **\"Worker places box on conveyor belt\"**\n", "  - **\"Box falls due to improper placement\"**\n", "\n", "These entities and relationships are represented as nodes and edges in a Neo4j graph. Captions and embeddings, generated with `llama-3.2-nv-embedqa-1b-v2`, are also linked to these entities. These can provide descriptive answers to user queries.\n"]}, {"cell_type": "markdown", "id": "c0014068-686a-4595-b030-6e5f7d5391ce", "metadata": {}, "source": ["---\n", "#### Graph-Retriever\n", "\n", "##### Cypher Query Generation:\n", "The Graph Retriever leverages an LLM to process user queries and translate them into structured cypher queries suitable for graph-based searches.\n", "\n", "##### Example:\n", "If the user query is:  \n", "*\"What caused the box to fall?\"*\n", "\n", "- The LLM identifies the key entities (e.g., \"box\") and the desired information (e.g., cause of fall).  \n", "- It then generates a structured cypher query for the graph:\n", "\n", "```cypher\n", "MATCH (b:Object)-[:PLACED_ON]->(c:Equipment), (b)-[:FALLS_DUE_TO]->(r:Reason)\n", "WHERE b.name = 'Box'\n", "RETURN r\n", "```\n", "\n", "This query, executed on the knowledge graph, retrieves the relevant information, enabling users to query complex relationships within the graph.\n"]}, {"cell_type": "markdown", "id": "82000117-356e-4349-9749-8243472b2c21", "metadata": {}, "source": ["---\n", "#### Graph-Generation\n", "\n", "Once the Graph Retriever processes the user query and fetches a relevant subgraph (entities, relationships, and captions) from the knowledge graph, **G-Generation** utilizes an LLM to analyze and synthesize the retrieved data into a coherent and meaningful response.\n", "\n", "##### Example:\n", "If the user query is:  \n", "*\"What caused the box to fall?\"*  \n", "\n", "The Graph Retriever might fetch the subgraph containing:\n", "- **Nodes**: \n", "  - Object (**Box**)\n", "  - Equipment (**Conveyor Belt**)\n", "  - Reason (**Improper Placement**)\n", "- **Relationships**:\n", "  - **\"Box placed on conveyor belt\"**\n", "  - **\"Box falls due to improper placement\"**\n", "- **Caption**:\n", "  - **\"A worker places a heavy box on the conveyor belt, and the box falls due to improper placement.\"**\n", "\n", "G-Generation processes this data, combining the graph structure and its properties, to generate a response such as:  \n", "*\"The box fell because it was improperly placed on the conveyor belt.\"*\n", "\n", "---"]}, {"cell_type": "markdown", "id": "421d43a8-1fec-47a4-baa4-396b9e1e3a1f", "metadata": {}, "source": ["#### 2.1 Let's Try a Few More Questions"]}, {"cell_type": "code", "execution_count": null, "id": "4ca6c738-3876-470c-8088-32e10614d49d", "metadata": {}, "outputs": [], "source": ["qna(\"What could be some possible safety issues in this warehouse?\")"]}, {"cell_type": "code", "execution_count": null, "id": "00d5f5b7-9fa0-494e-9780-76f727597667", "metadata": {}, "outputs": [], "source": ["qna(\"When did the forklift appear?\")"]}, {"cell_type": "code", "execution_count": null, "id": "a522cd29-fe91-4b68-9f48-1af1487abab7", "metadata": {}, "outputs": [], "source": ["qna(\"Describe the warehouse setting in detail.\")"]}, {"cell_type": "code", "execution_count": null, "id": "c9912f90-374d-4917-bb20-f8092ab520e1", "metadata": {}, "outputs": [], "source": ["# qna(\"Enter your question\")"]}, {"cell_type": "code", "execution_count": null, "id": "53154747-e787-4c2d-be15-d7a1278337fa", "metadata": {}, "outputs": [], "source": ["# qna(\"Enter your question\")"]}, {"cell_type": "markdown", "id": "aaca0655-f131-439f-9e0b-eb57c6b96bfa", "metadata": {}, "source": ["---\n", "### Part 3: Graph-RAG Visualization\n", "\n", "In this section, we will explore and visualize the knowledge graph stored in the Neo4j database. By leveraging the Neo4j Python library, we will run queries to fetch specific parts of the graph and render them visually for better understanding. This visualization helps in inspecting the structure and relationships in the graph, providing a clear representation of the data stored in the database."]}, {"cell_type": "markdown", "id": "6725cd14-3edd-4712-99db-d2544c0bfdb6", "metadata": {}, "source": ["##### Sample graph from Neo4j Visualizer Dashboard\n", "\n", "<img alt=\"Graph Diagram\" src=\"assets/graph_neo4j.png\" width=1000>"]}, {"cell_type": "code", "execution_count": null, "id": "a0b5cc4c-a8ce-4979-be2b-6fa1eef4a460", "metadata": {}, "outputs": [], "source": ["# Helper functions - No need to understand the following code cell\n", "\n", "from py2neo import Graph\n", "import networkx as nx\n", "import matplotlib.pyplot as plt\n", "import textwrap\n", "\n", "def visualize_neo4j_query(query, host=\"localhost\", port=7687, user=\"neo4j\", password=\"password\"):\n", "    try:\n", "        graph = Graph(f\"bolt://{host}:{port}\", auth=(user, password))\n", "    except Exception as e:\n", "        print(f\"Error connecting to Neo4j: {e}\")\n", "        return\n", "\n", "    try:\n", "        result = graph.run(query)\n", "        G = nx.DiGraph()\n", "\n", "        for record in result:\n", "            path = record[\"p\"]\n", "            for rel in path.relationships:\n", "                start_node = rel.start_node\n", "                end_node = rel.end_node\n", "\n", "                start_label = start_node.get(\"name\", start_node.get(\"id\", f\"Node_{start_node.identity}\"))\n", "                end_label = end_node.get(\"name\", end_node.get(\"id\", f\"Node_{end_node.identity}\"))\n", "\n", "                # Wrap labels for better readability if they are too long\n", "                start_label = '\\n'.join(textwrap.wrap(start_label, width=20))\n", "                end_label = '\\n'.join(textwrap.wrap(end_label, width=20))\n", "\n", "                G.add_node(start_label)\n", "                G.add_node(end_label)\n", "                G.add_edge(start_label, end_label, label=rel.__class__.__name__)\n", "\n", "        plt.figure(figsize=(15, 10))\n", "        \n", "        pos = nx.spring_layout(G, seed=42, k=0.5, iterations=50)\n", "        \n", "        nx.draw_networkx_nodes(G, pos, node_color='lightgreen', node_size=2500)\n", "        nx.draw_networkx_labels(G, pos, font_size=8)\n", "        edges = nx.draw_networkx_edges(G, pos, arrowstyle='-|>', arrowsize=10)\n", "        edge_labels = nx.get_edge_attributes(G, 'label')\n", "        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_color='blue')\n", "\n", "        plt.title(\"Neo4j Graph Visualization\")\n", "        plt.show()\n", "\n", "    except Exception as e:\n", "        print(f\"Error running query or visualizing the graph: {e}\")\n", "\n", "\n", "def get_neo4j_query_text(query, host=\"localhost\", port=7687, user=\"neo4j\", password=\"password\"):\n", "    try:\n", "        graph = Graph(f\"bolt://{host}:{port}\", auth=(user, password))\n", "    except Exception as e:\n", "        print(f\"Error connecting to Neo4j: {e}\")\n", "        return\n", "\n", "    try:\n", "        result = graph.run(query)\n", "        output = []\n", "\n", "        for record in result:\n", "            path = record[\"p\"]\n", "            for rel in path.relationships:\n", "                start_node = rel.start_node\n", "                end_node = rel.end_node\n", "\n", "                start_label = start_node.get(\"name\", start_node.get(\"id\", f\"Node_{start_node.identity}\"))\n", "                end_label = end_node.get(\"name\", end_node.get(\"id\", f\"Node_{end_node.identity}\"))\n", "                rel_type = type(rel).__name__\n", "\n", "                output.append(f\"{start_label} - {rel_type} - {end_label}\")\n", "\n", "        if not output:\n", "            return \"No results found.\"\n", "\n", "        return \"\\n\".join(output)\n", "\n", "    except Exception as e:\n", "        print(f\"Error running query or processing the results: {e}\")\n", "        return None\n", "\n"]}, {"cell_type": "markdown", "id": "960ccc0b-da3c-4556-ba3a-e2577b55e2d9", "metadata": {}, "source": ["---\n", "#### 3.1 Cypher Queries\n", "\n", "<span style=\"color:red\"><b>NOTE: You might have to modify the entity and relationship names in the following cypher queries based on the actual generated graph</b></span>\n", "\n", "##### Visualizing Who Wears What\n", "\n", "Let's see how the sub-graph related to all entities with keywork \"WEARS\" looks like:\n", "\n", "The following Cypher query retrieves and visualizes relationships where people are wearing items. It matches all `WEARS` relationships in the graph and returns the paths to better understand the connections.\n", "\n", "**Cypher Query:**\n", "```cypher\n", "MATCH p=()-[r:WEARS]->() \n", "RETURN p\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "6c884591-0909-4f27-8c14-5c30ffe26867", "metadata": {}, "outputs": [], "source": ["visualize_neo4j_query(\"MATCH p=()-[r:WEARS]->() RETURN p\")"]}, {"cell_type": "markdown", "id": "ed3d2e5a-0ce3-430f-84df-bfaa559a0b83", "metadata": {}, "source": ["##### Visualizing Sub-Graph where a Person with id='worker' is shown with WEARS relation/edge\n", "\n", "The following Cypher query retrieves information about a specific person (identified by `worker`) and what the worker is wearing. It matches the `WEARS` relationship between the person and the clothing item, returning the path and details of the item.\n", "\n", "**Cypher Query:**\n", "\n", "```cypher\n", "MATCH p=(person)-[r:WEARS]->(item)\n", "WHERE person.id = 'worker'\n", "RETURN p\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "id": "f7f197ef-4a19-40ff-9ce3-1d5111fd5def", "metadata": {}, "outputs": [], "source": ["visualize_neo4j_query(\"MATCH p=(person)-[r:WEARS]->(item) WHERE person.id = 'worker' RETURN p\")"]}, {"cell_type": "markdown", "id": "ba7631d8-c903-4637-81d3-5dbb075063f2", "metadata": {}, "source": ["##### Fetching a particular nodes with Cypher Query\n", "\n", "Suppose you want to ask a question where the agent needs to know what is located in the warehouse.\n", "\n", "The following Cypher query retrieves information about objects which are located in spaces starting with \"warehouse...\". This is denoted by the item `id` = \"warehouse*\". It matches the `LOCATED_IN` relationship between objects and the location, and returns the details of each node.\n", "\n", "**Cypher Query:**\n", "\n", "```cypher\n", "MATCH p=(person)-[r:LOCATED_IN]->(item)\n", "WHERE item.id =~ \"warehouse.*\"\n", "RETURN p\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "dffa7d5c-278f-44f5-b52d-4ff72aa2fa03", "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "MATCH p=(person)-[r:LOCATED_IN]->(item)\n", "WHERE item.id =~ \"warehouse.*\"\n", "RETURN p\n", "\"\"\"\n", "\n", "text_output = get_neo4j_query_text(query)\n", "print(text_output)"]}, {"cell_type": "markdown", "id": "3a37f75c-fc2f-4538-8e5b-a51879159782", "metadata": {}, "source": ["---\n", "### Part 4: Enhancing video understanding - Audio transcription and CV Pipeline\n", "\n", "VSS 2.3.0 GA release includes several new features including multi-live stream, burst mode ingestion, CV pipeline, and audio transcription.\n", "\n", "- **Multi-live stream and burst clip modes**: Concurrently process hundreds of live streams or pre-recorded video files. This is useful to scale your video AI agents.\n", "- **Audio transcription**: Convert speech to text for a multimodal understanding of a scene. This is useful for use cases where audio is a key component, such as instructional videos, keynotes, team meetings, or company training content.\n", "- **Computer vision pipeline**: Enhance accuracy by tracking objects in a scene with zero-shot object detection and using bounding boxes and segmentation masks with Set-of-Mark (SoM), which guides vision-language models using a predefined set of reference points or labels to improve detection.\n", "\n"]}, {"cell_type": "markdown", "id": "9569bcf6-8786-4eba-99d0-b139b2a8c7d3", "metadata": {}, "source": ["#### 4.1: Audio Transcription\n", "\n", "By default, VSS uses Riva ASR NIM, which is a state-of-the-art automatic speech recognition (ASR) models for multiple languages. \n", "\n", "To enable audio transcription integration in VSS ingestion pipeline, make sure to set ```ENABLE_AUDIO=true``` in .env while setting up VSS, and provide details for RIVA ASR model in the configuration.\n", "\n", "- [Enabling audio with Docker Compose](https://docs.nvidia.com/vss/latest/content/vss_dep_docker_compose_x86.html#deploy-riva-asr-nim-optional)\n", "- [Enabling audio with He<PERSON> Chart](https://docs.nvidia.com/vss/latest/content/vss_dep_helm.html#enabling-audio)\n", "\n", "We will explore this feature with a new video which contains audio. This is from GTC Keynote 2025.\n", "\n", "<video width=\"1000 \" height=\" \" \n", "       src=\"assets/keynote_clip.mp4\"  \n", "       controls>\n", "</video>"]}, {"cell_type": "code", "execution_count": null, "id": "b1a7c063-fb4f-4985-b720-c48ab0a00606", "metadata": {}, "outputs": [], "source": ["with open(keynote_video, \"rb\") as file:\n", "    files = {\"file\": (\"keynote_video\", file)} #provide the file content along with a file name \n", "    data = {\"purpose\":\"vision\", \"media_type\":\"video\"}\n", "    response1 = requests.post(upload_file_endpoint, data=data, files=files) #post file upload request \n", "response1 = check_response(response1)\n", "keynote_video_id_1 = response1[\"id\"] #save file ID for summarization request without audio\n", "\n", "with open(keynote_video, \"rb\") as file:\n", "    files = {\"file\": (\"keynote_video\", file)} #provide the file content along with a file name \n", "    data = {\"purpose\":\"vision\", \"media_type\":\"video\"}\n", "    response2 = requests.post(upload_file_endpoint, data=data, files=files) #post file upload request \n", "response2 = check_response(response2)\n", "keynote_video_id_2 = response2[\"id\"] #save file ID for summarization request with audio"]}, {"cell_type": "code", "execution_count": null, "id": "544a50f4-ecb9-41c9-973f-c015d43326d2", "metadata": {}, "outputs": [], "source": ["prompts = {\n", "    \"vlm_prompt\": \"Write a concise and clear dense caption for the provided NVIDIA GTC Keynote video presented by <PERSON>, focusing on the technology launches and visual presentation\",\n", "    \n", "    \"caption_summarization\": \"You should summarize the following events of a conference. The output should be in bullet points with timestamps. Do not return anything else except the bullet points.\",\n", "    \n", "    \"summary_aggregation\": \"You are a video description service. Given the video captions and audio transcripts, aggregate them to a concise summary with timestamps. The output should only contain bullet points.\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f78df832-0839-4a3f-9448-f0964c7b34d1", "metadata": {}, "outputs": [], "source": ["payload = {\n", "    \"id\": keynote_video_id_1,\n", "    \"prompt\": prompts['vlm_prompt'],\n", "    \"caption_summarization_prompt\": prompts['caption_summarization'],\n", "    \"summary_aggregation_prompt\": prompts['summary_aggregation'],\n", "    \"model\": configured_vlm,\n", "    \"chunk_duration\": 10,\n", "    \"chunk_overlap_duration\": 0,\n", "    \"summarize\": True,  \n", "    \"enable_chat\": True,\n", "    \"enable_audio\": False  #Processing video without audio\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=payload)\n", "response = check_response(response)\n", "summary_no_audio = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "10e0fa96-4d81-4562-b0a1-45e78b9e9e05", "metadata": {}, "outputs": [], "source": ["payload = {\n", "    \"id\": keynote_video_id_2,\n", "    \"prompt\": prompts['vlm_prompt'],\n", "    \"caption_summarization_prompt\": prompts['caption_summarization'],\n", "    \"summary_aggregation_prompt\": prompts['summary_aggregation'],\n", "    \"model\": configured_vlm,\n", "    \"chunk_duration\": 10,\n", "    \"chunk_overlap_duration\": 0,\n", "    \"summarize\": True,  \n", "    \"enable_chat\": True,\n", "    \"enable_audio\": True  #Processing video with audio\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=payload)\n", "response = check_response(response)\n", "summary_with_audio = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "efc5a754-58f0-472a-9649-78c6a6d1ce16", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "  <h2> Without Audio </h2>\n", "    {summary_no_audio}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "  <h2> With Audio </h2>\n", "    \\n{summary_with_audio}\n", "  </div>\n", "</div>\n", "\"\"\"\n", "\n", "Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "c7ba3139-c5aa-488d-b8aa-0c5e34051304", "metadata": {}, "source": ["Notice how we were able to get more accurate summaries by enabling audio, keeping all other parameters the same. This could be useful in transcription heavy videos like training sessions, lectures, instructional videos, etc. \n", "\n", "Now, let's look into another feature that could be easily enabled within VSS."]}, {"cell_type": "markdown", "id": "4231c02e-7e2b-4e34-b81c-a400c61e4123", "metadata": {}, "source": ["---\n", "#### 4.2: Computer Vision Pipeline\n", "\n", "The CV metadata generated by the CV pipeline is utilized to improve the accuracy of Video Search and Summarization in two ways:\n", "\n", "* Metadata is used by the data processing pipeline to generate inputs for VLM with overlaid object ID, masks etc. This helps in improving the accuracy of VLM as well as enables Set of Marks prompting.\n", "\n", "* Metadata is attached with VLM generated dense captions and passed to retrieval pipeline for further processing and indexing.\n", "\n", "Following are the steps to initialize the CV pipeline in VSS. Once initialized, users can choose to enable or disable the CV pipeline for individual summarization requests.\n", "\n", "Let's look at this using a sample video used in the previous notebook - traffic intersection.\n", "\n", "<video width=\"1000 \" height=\" \" \n", "       src=\"data/traffic.mp4\"  \n", "       controls>\n", "</video>"]}, {"cell_type": "code", "execution_count": null, "id": "64d9766f-5acf-462a-a7de-97eff498725d", "metadata": {}, "outputs": [], "source": ["with open(traffic_video, \"rb\") as file:\n", "    files = {\"file\": (\"traffic_video\", file)} #provide the file content along with a file name \n", "    data = {\"purpose\":\"vision\", \"media_type\":\"video\"}\n", "    response1 = requests.post(upload_file_endpoint, data=data, files=files) #post file upload request \n", "response1 = check_response(response1)\n", "traffic_video_id = response1[\"id\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0acae43c-47c8-44f7-bcfd-9ddcec9842b8", "metadata": {}, "outputs": [], "source": ["prompts = {\n", "    \"vlm_prompt\": \"You are an intelligent traffic system. You must monitor and take note of all traffic related events. Start each event description with a start and end time stamp.\",\n", "    \n", "    \"caption_summarization\": \"You will be given captions from sequential clips of a video. Aggregate captions in the format start_time:end_time:caption based on whether captions are related to one another or create a continuous scene\",\n", "    \n", "    \"summary_aggregation\": \"Based on the available information, generate a traffic report that is organized chronologically and in logical sections.Give each section a descriptive heading of what occurs and the time range. This should be a concise, yet descriptive summary of all the important events. The format should be intuitive and easy for a user to read and understand what happened. Format the output in Markdown so it can be displayed nicely.\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1ebdbd73-94b6-49c5-b2d3-4836ff652021", "metadata": {}, "outputs": [], "source": ["payload = {\n", "    \"id\": traffic_video_id,\n", "    \"prompt\": prompts['vlm_prompt'],\n", "    \"caption_summarization_prompt\": prompts['caption_summarization'],\n", "    \"summary_aggregation_prompt\": prompts['summary_aggregation'],\n", "    \"model\": configured_vlm,\n", "    \"chunk_duration\": 10,\n", "    \"chunk_overlap_duration\": 0,\n", "    \"summarize\": True,  \n", "    \"enable_chat\": True,\n", "    \"enable_cv_metadata\": False  #Processing video without CV metadata\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=payload)\n", "response = check_response(response)\n", "summary_no_cv = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "7ce44f41-81c3-4627-bbdb-1b435d1a6901", "metadata": {}, "outputs": [], "source": ["answer_no_cv = qna(\"Which cars collided?\", traffic_video_id)\n", "print(answer_no_cv)"]}, {"cell_type": "markdown", "id": "8bb474b1-01c7-462a-aa80-2b1d7f15a29e", "metadata": {}, "source": ["Now, for effectivaly using CV metadata, we need to update the first prompt as shown below, so that VLM uses IDs in event descriptions."]}, {"cell_type": "code", "execution_count": null, "id": "64b5d595-b4b7-4215-9f19-c8f290ccf3f8", "metadata": {}, "outputs": [], "source": ["updated_vlm_prompt = (\n", "    \"You are an intelligent traffic system. The provided video is \"\n", "    \"a processed clip where each vehicle is overlaid with an ID. \"\n", "    \"You must monitor and take note of all traffic related events. \"\n", "    \"Start each event description with a start and end time stamp \"\n", "    \"of the event, and use vehicle IDs in event description.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "89ce5ba8-9f9d-48be-9072-5719e8b9798d", "metadata": {}, "outputs": [], "source": ["payload = {\n", "    \"id\": traffic_video_id,\n", "    \"prompt\": updated_vlm_prompt,\n", "    \"caption_summarization_prompt\": prompts['caption_summarization'],\n", "    \"summary_aggregation_prompt\": prompts['summary_aggregation'],\n", "    \"model\": configured_vlm,\n", "    \"chunk_duration\": 10,\n", "    \"chunk_overlap_duration\": 0,\n", "    \"summarize\": True,  \n", "    \"enable_chat\": True,\n", "    \"enable_cv_metadata\": True,  #Processing video with CV metadata\n", "    \"cv_pipeline_prompt\": \"vehicle\"\n", "}\n", "\n", "response = requests.post(summarize_endpoint, json=payload)\n", "response = check_response(response)\n", "summary_with_cv = response[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "d6361353-904b-4ef6-960f-62f339831baf", "metadata": {}, "outputs": [], "source": ["answer_with_cv = qna(\"Which cars collided?\", traffic_video_id)\n", "print(answer_with_cv)"]}, {"cell_type": "code", "execution_count": null, "id": "d87c4784-78c4-4f3a-96b2-f156cbbc59f1", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "    <h2>Summary without CV Metadata</h2>\n", "    {summary_no_cv}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "    <h2>Summary with CV Metadata</h2>\n", "    {summary_with_cv}\n", "  </div>\n", "</div>\n", "\"\"\"\n", "\n", "Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "13d22fbe-45a9-434b-a878-2d30b4aabe7f", "metadata": {}, "source": ["Finally lets compare the Q&A results"]}, {"cell_type": "code", "execution_count": null, "id": "6282f84a-605d-4392-b9ea-d2636006d64e", "metadata": {}, "outputs": [], "source": ["markdown_string = f\"\"\"\n", "<div style=\"display: flex; gap: 20px;\">\n", "  <div style=\"flex: 1;\">\n", "    <h2>Q&A without CV Metadata</h2>\n", "    {answer_no_cv}\n", "  </div>\n", "  <div style=\"flex: 1;\">\n", "    <h2>Q&A with CV Metadata</h2>\n", "    {answer_with_cv}\n", "  </div>\n", "</div>\n", "\"\"\"\n", "\n", "Markdown(markdown_string)"]}, {"cell_type": "markdown", "id": "d2e60be0-d6f6-4688-9e8f-5db486d5152b", "metadata": {}, "source": ["With CV metadata enabled, VLM was able to use overlayed vehicles IDs and give context in the summary. This makes it easier to ground information to the original input video. "]}, {"cell_type": "markdown", "id": "7cfb841b-9557-4a92-8f09-034ae80ff167", "metadata": {}, "source": ["---\n", "### Review\n", "\n", "In this notebook you learned the following:\n", "- How to enable chat in VSS to ask questions about a video\n", "- How Graph-RAG extraction from dense captions works under the hood\n", "- How to visualize the knowledge graph to understand its structure and relationships\n", "- How to enable and use features like audio transcription and CV metadata to improve accuracy for specific use-cases"]}, {"cell_type": "markdown", "id": "29383f85-76ae-4a1c-86ff-1893a9f3dc29", "metadata": {}, "source": ["---\n", "### [Additional Material]: Finetuning a Custom VLM\n", "\n", "In Video Search and Summarization (VSS), users can leverage a custom or fine-tuned Vision-Language Model (VLM).\n", "\n", "To support this, NVIDIA provides a set of resources for finetuning VLMs using the TAO FTMS service.\n", "\n", "#### Finetuning VLMs with Train Adapt and Optimize Finetuning Microservices (TAO FTMS)\n", "\n", "We offer a container and resources via [NGC](https://catalog.ngc.nvidia.com/orgs/nvidia/teams/tao/containers/vlm-lita-finetuning-ea)\n", "\n", "#### Reference Notebooks\n", "\n", "You can find the full set of [example notebooks](https://catalog.ngc.nvidia.com/orgs/nvidia/teams/tao/resources/vlm-lita-getting-started-ea/files) for Standard Operating Procedure (SOP) usecase\n", "\n", "**Step-1. Data Preparation for SOP Use Case: `01_Data_Labeling.ipynb`**\n", "- Video chunking (frame/window segmentation)\n", "- Label creation in LLaVA-compatible format\n", "- Supports: MCQ (Multiple Choice QA), GQA (General QA), and BQA (Binary QA)\n", "\n", "**Step-2. Finetuning the VLM with TAO FTMS: `vila.ipynb`**\n", "- Setup for training using the VLM LITA container\n", "- Use the curated videos and labels generated in Step-1\n", "\n", "**Step-3. SOP Agent (Standard Operating Procedure): `03_VLM_Agent.ipynb`**\n", "- Example QA agent to test VLM capabilities\n", "- Compare model outputs before and after fine-tuning\n", "\n", "> **Tip:** Use this setup to enhance VLM performance on your domain-specific video data, and you can follow the [steps](https://docs.nvidia.com/vss/latest/content/installation-vlms.html#local-ngc-models-vila-nvila) to integrate finetuned vlm in VSS."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}