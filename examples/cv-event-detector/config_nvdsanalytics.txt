################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2020-2022 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
################################################################################

# The values in the config file are overridden by values set through GObject
# properties.

[property]
enable=1
#Width height used for configuration to which below configs are configured
config-width=1920
config-height=1080
#osd-mode 0: Dont display any lines, rois and text
#         1: Display only lines, rois and static text i.e. labels
#         2: Display all info from 1 plus information about counts
osd-mode=2
#Set OSD font size that has to be displayed
display-font-size=12

## Per stream configuration
[roi-filtering-stream-0]
#enable or disable following feature
enable=0
#ROI to filter select objects, and remove from meta data
roi-RF=295;643;579;634;642;913;56;828
#remove objects in the ROI
inverse-roi=0
class-id=-1


[overcrowding-stream-0]
#enable or disable following feature
enable=1
#ROI to filter select objects, and remove from meta data
#roi-OC=295;643;579;634;642;913;56;828
roi-OC=0;0;1919;0;1919;1079;0;1079
#remove objects in the ROI
#inverse-roi=0
object-threshold=1
class-id=-1
#roi-latency=100


[line-crossing-stream-0]
enable=0
#Label;direction;lc
#line-crossing-Entry=1072;911;1143;1058;944;1020;1297;1020;
line-crossing-Exit=789;672;1084;900;851;773;1203;732
class-id=0
#extended when 0- only counts crossing on the configured Line
#              1- assumes extended Line crossing counts all the crossing
extended=0
#LC modes supported:
#loose   : counts all crossing without strong adherence to direction
#balanced: Strict direction adherence expected compared to mode=loose
#strict  : Strict direction adherence expected compared to mode=balanced
mode=loose

