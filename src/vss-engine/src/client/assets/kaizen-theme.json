{"theme": {"_font": [{"__gradio_font__": true, "name": "NVIDIA Sans", "class": "font"}, {"__gradio_font__": true, "name": "ui-sans-serif", "class": "font"}, {"__gradio_font__": true, "name": "system-ui", "class": "font"}, {"__gradio_font__": true, "name": "sans-serif", "class": "font"}], "_font_mono": [{"__gradio_font__": true, "name": "JetBrains Mono", "class": "google"}, {"__gradio_font__": true, "name": "ui-monospace", "class": "font"}, {"__gradio_font__": true, "name": "Consolas", "class": "font"}, {"__gradio_font__": true, "name": "monospace", "class": "font"}], "_stylesheets": ["https://fonts.googleapis.com/css2?family=JetBrains+Mono&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_Lt.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_LtIt.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_Rg.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_It.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_Md.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_MdIt.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_Bd.woff2", "https://brand-assets.cne.ngc.nvidia.com/assets/fonts/nvidia-sans/1.0.0/NVIDIASans_BdIt.woff2"], "background_fill_primary": "#f5f5f5", "background_fill_primary_dark": "#f5f5f5", "background_fill_secondary": "*neutral_50", "background_fill_secondary_dark": "*neutral_50", "block_background_fill": "#f5f5f5", "block_background_fill_dark": "#f5f5f5", "block_border_color": "#d8d8d8", "block_border_color_dark": "#d8d8d8", "block_border_width": "1px", "block_info_text_color": "*body_text_color_subdued", "block_info_text_color_dark": "*body_text_color_subdued", "block_info_text_size": "*text_sm", "block_info_text_weight": "400", "block_label_background_fill": "#e4fabe", "block_label_background_fill_dark": "#e4fabe", "block_label_border_color": "#e4fabe", "block_label_border_color_dark": "#e4fabe", "block_label_border_width": "1px", "block_label_margin": "0", "block_label_padding": "*spacing_sm *spacing_lg", "block_label_radius": "calc(*radius_lg - 1px) 0 calc(*radius_lg - 1px) 0", "block_label_right_radius": "0 calc(*radius_lg - 1px) 0 calc(*radius_lg - 1px)", "block_label_shadow": "*block_shadow", "block_label_text_color": "#4d6721", "block_label_text_color_dark": "#4d6721", "block_label_text_size": "*text_sm", "block_label_text_weight": "400", "block_padding": "*spacing_xl calc(*spacing_xl + 2px)", "block_radius": "*radius_lg", "block_shadow": "*shadow_drop", "block_title_background_fill": "none", "block_title_border_color": "none", "block_title_border_width": "0px", "block_title_padding": "0", "block_title_radius": "none", "block_title_text_color": "*neutral_500", "block_title_text_color_dark": "*neutral_500", "block_title_text_size": "*text_md", "block_title_text_weight": "500", "body_background_fill": "#f5f5f5", "body_background_fill_dark": "#f5f5f5", "body_text_color": "#202020", "body_text_color_dark": "#202020", "body_text_color_subdued": "*neutral_400", "body_text_color_subdued_dark": "*neutral_400", "body_text_size": "*text_md", "body_text_weight": "400", "border_color_accent": "*primary_300", "border_color_accent_dark": "*primary_300", "border_color_primary": "#d8d8d8", "border_color_primary_dark": "#d8d8d8", "button_border_width": "1px", "button_border_width_dark": "1px", "button_cancel_background_fill": "#dc3528", "button_cancel_background_fill_dark": "#dc3528", "button_cancel_background_fill_hover": "#b6251b", "button_cancel_background_fill_hover_dark": "#b6251b", "button_cancel_border_color": "#dc3528", "button_cancel_border_color_dark": "#dc3528", "button_cancel_border_color_hover": "#b6251b", "button_cancel_border_color_hover_dark": "#b6251b", "button_cancel_text_color": "#ffffff", "button_cancel_text_color_dark": "#ffffff", "button_cancel_text_color_hover": "#ffffff", "button_cancel_text_color_hover_dark": "#ffffff", "button_large_padding": "*spacing_lg calc(2 * *spacing_lg)", "button_large_radius": "*radius_lg", "button_large_text_size": "*text_lg", "button_large_text_weight": "500", "button_primary_background_fill": "#76b900", "button_primary_background_fill_dark": "#76b900", "button_primary_background_fill_hover": "#659f00", "button_primary_background_fill_hover_dark": "#659f00", "button_primary_border_color": "#76b900", "button_primary_border_color_dark": "#76b900", "button_primary_border_color_hover": "#659f00", "button_primary_border_color_hover_dark": "#659f00", "button_primary_text_color": "#202020", "button_primary_text_color_dark": "#202020", "button_primary_text_color_hover": "#202020", "button_primary_text_color_hover_dark": "#202020", "button_secondary_background_fill": "#ffffff", "button_secondary_background_fill_dark": "#ffffff", "button_secondary_background_fill_hover": "#e2e2e2", "button_secondary_background_fill_hover_dark": "#e2e2e2", "button_secondary_border_color": "#5e5e5e", "button_secondary_border_color_dark": "#5e5e5e", "button_secondary_border_color_hover": "#5e5e5e", "button_secondary_border_color_hover_dark": "#5e5e5e", "button_secondary_text_color": "#5e5e5e", "button_secondary_text_color_dark": "#5e5e5e", "button_secondary_text_color_hover": "#343434", "button_secondary_text_color_hover_dark": "#343434", "button_shadow": "*shadow_drop", "button_shadow_active": "*shadow_inset", "button_shadow_hover": "*shadow_drop_lg", "button_small_padding": "*spacing_sm calc(2 * *spacing_sm)", "button_small_radius": "*radius_lg", "button_small_text_size": "*text_md", "button_small_text_weight": "400", "button_transition": "none", "chatbot_code_background_color": "*neutral_100", "chatbot_code_background_color_dark": "*neutral_100", "checkbox_background_color": "*background_fill_primary", "checkbox_background_color_dark": "*background_fill_primary", "checkbox_background_color_focus": "*checkbox_background_color", "checkbox_background_color_focus_dark": "*checkbox_background_color", "checkbox_background_color_hover": "*checkbox_background_color", "checkbox_background_color_hover_dark": "*checkbox_background_color", "checkbox_background_color_selected": "#659f00", "checkbox_background_color_selected_dark": "#659f00", "checkbox_border_color": "*neutral_300", "checkbox_border_color_dark": "*neutral_300", "checkbox_border_color_focus": "*secondary_500", "checkbox_border_color_focus_dark": "*secondary_500", "checkbox_border_color_hover": "*neutral_300", "checkbox_border_color_hover_dark": "*neutral_300", "checkbox_border_color_selected": "#659f00", "checkbox_border_color_selected_dark": "#659f00", "checkbox_border_radius": "*radius_sm", "checkbox_border_width": "2px", "checkbox_border_width_dark": "2px", "checkbox_check": "url(\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\")", "checkbox_label_background_fill": "#ffffff", "checkbox_label_background_fill_dark": "#ffffff", "checkbox_label_background_fill_hover": "#ffffff", "checkbox_label_background_fill_hover_dark": "#ffffff", "checkbox_label_background_fill_selected": "*checkbox_label_background_fill", "checkbox_label_background_fill_selected_dark": "*checkbox_label_background_fill", "checkbox_label_border_color": "#ffffff", "checkbox_label_border_color_dark": "#ffffff", "checkbox_label_border_color_hover": "*checkbox_label_border_color", "checkbox_label_border_color_hover_dark": "*checkbox_label_border_color", "checkbox_label_border_width": "0", "checkbox_label_border_width_dark": "0", "checkbox_label_gap": "16px", "checkbox_label_padding": "", "checkbox_label_shadow": "none", "checkbox_label_text_color": "*body_text_color", "checkbox_label_text_color_dark": "*body_text_color", "checkbox_label_text_color_selected": "*checkbox_label_text_color", "checkbox_label_text_color_selected_dark": "*checkbox_label_text_color", "checkbox_label_text_size": "*text_md", "checkbox_label_text_weight": "400", "checkbox_shadow": "*input_shadow", "color_accent": "*primary_500", "color_accent_soft": "*primary_50", "color_accent_soft_dark": "*primary_50", "container_radius": "*radius_lg", "embed_radius": "*radius_lg", "error_background_fill": "#fef2f2", "error_background_fill_dark": "#fef2f2", "error_border_color": "#fee2e2", "error_border_color_dark": "#fee2e2", "error_border_width": "1px", "error_icon_color": "#b91c1c", "error_icon_color_dark": "#b91c1c", "error_text_color": "#b91c1c", "error_text_color_dark": "#b91c1c", "font": "'NVIDIA Sans', 'ui-sans-serif', 'system-ui', sans-serif", "font_mono": "'JetBrains Mono', 'ui-monospace', 'Consolas', monospace", "form_gap_width": "5px", "input_background_fill": "white", "input_background_fill_dark": "white", "input_background_fill_focus": "*secondary_500", "input_background_fill_focus_dark": "*secondary_500", "input_background_fill_hover": "*input_background_fill", "input_background_fill_hover_dark": "*input_background_fill", "input_border_color": "#d8d8d8", "input_border_color_dark": "#d8d8d8", "input_border_color_focus": "*secondary_300", "input_border_color_focus_dark": "*secondary_300", "input_border_color_hover": "*input_border_color", "input_border_color_hover_dark": "*input_border_color", "input_border_width": "2px", "input_padding": "*spacing_xl", "input_placeholder_color": "*neutral_400", "input_placeholder_color_dark": "*neutral_400", "input_radius": "*radius_lg", "input_shadow": "0 0 0 *shadow_spread transparent, *shadow_inset", "input_shadow_focus": "0 0 0 *shadow_spread *secondary_50, *shadow_inset", "input_shadow_focus_dark": "0 0 0 *shadow_spread *secondary_50, *shadow_inset", "input_text_size": "*text_md", "input_text_weight": "400", "layout_gap": "*spacing_xxl", "link_text_color": "*secondary_600", "link_text_color_active": "*secondary_600", "link_text_color_active_dark": "*secondary_600", "link_text_color_dark": "*secondary_500", "link_text_color_hover": "*secondary_700", "link_text_color_hover_dark": "*secondary_700", "link_text_color_visited": "*secondary_500", "link_text_color_visited_dark": "*secondary_500", "loader_color": "*color_accent", "name": "default", "neutral_100": "#e2e2e2", "neutral_200": "#d8d8d8", "neutral_300": "#c6c6c6", "neutral_400": "#8f8f8f", "neutral_50": "#f2f2f2", "neutral_500": "#767676", "neutral_600": "#5e5e5e", "neutral_700": "#343434", "neutral_800": "#292929", "neutral_900": "#202020", "neutral_950": "#121212", "panel_background_fill": "*background_fill_secondary", "panel_background_fill_dark": "*background_fill_secondary", "panel_border_color": "*border_color_primary", "panel_border_color_dark": "*border_color_primary", "panel_border_width": "0", "primary_100": "#caf087", "primary_200": "#b6e95d", "primary_300": "#9fd73d", "primary_400": "#76b900", "primary_50": "#e4fabe", "primary_500": "#659f00", "primary_600": "#538300", "primary_700": "#4d6721", "primary_800": "#253a00", "primary_900": "#1d2e00", "primary_950": "#172400", "prose_header_text_weight": "600", "prose_text_size": "*text_md", "prose_text_weight": "400", "radio_circle": "url(\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\")", "radius_lg": "0px", "radius_md": "0px", "radius_sm": "0px", "radius_xl": "0px", "radius_xs": "0px", "radius_xxl": "0px", "radius_xxs": "0px", "secondary_100": "#cde6fa", "secondary_200": "#badef8", "secondary_300": "#9accf2", "secondary_400": "#3a96d9", "secondary_50": "#e9f4fb", "secondary_500": "#2378ca", "secondary_600": "#2a63ba", "secondary_700": "#013076", "secondary_800": "#00265e", "secondary_900": "#001e4b", "secondary_950": "#00112c", "section_header_text_size": "*text_md", "section_header_text_weight": "500", "shadow_drop": "none", "shadow_drop_lg": "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)", "shadow_inset": "rgba(0,0,0,0.05) 0px 2px 4px 0px inset", "shadow_spread": "3px", "shadow_spread_dark": "3px", "slider_color": "#9fd73d", "spacing_lg": "8px", "spacing_md": "6px", "spacing_sm": "4px", "spacing_xl": "10px", "spacing_xs": "2px", "spacing_xxl": "10px", "spacing_xxs": "1px", "stat_background_fill": "linear-gradient(to right, *primary_400, *primary_200)", "stat_background_fill_dark": "linear-gradient(to right, *primary_400, *primary_200)", "table_border_color": "*neutral_300", "table_border_color_dark": "*neutral_300", "table_even_background_fill": "white", "table_even_background_fill_dark": "white", "table_odd_background_fill": "*neutral_50", "table_odd_background_fill_dark": "*neutral_50", "table_radius": "*radius_lg", "table_row_focus": "*color_accent_soft", "table_row_focus_dark": "*color_accent_soft", "text_lg": "16px", "text_md": "14px", "text_sm": "12px", "text_xl": "22px", "text_xs": "10px", "text_xxl": "26px", "text_xxs": "9px"}}