######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
######################################################################################################

name: "ensemble_python_gdino"
platform: "ensemble"
max_batch_size: 8
input [
  {
    name: "inputs"
    data_type: TYPE_FP32
    dims: [3,544,960]
  },
  {
    name: "PROMPT"
    data_type: TYPE_UINT8
    dims: [2048]
  }
]
output [
{
    name: "labels"
    data_type: TYPE_INT32
    dims: [-1,300]
    #dims: [-1,-1]
},
{
    name: "boxes"
    data_type: TYPE_FP32
    dims: [-1,300, 4]
    #dims: [-1,-1, 4]
}
,
{
    name: "scores"
    data_type: TYPE_FP32
    #dims: [-1,-1]
    dims: [-1,300]
}
]
ensemble_scheduling {
  step [
    {
      model_name: "gdino_preprocess"
      model_version: -1
      input_map [
      {
        key: "PROMPT"
        value: "PROMPT"
      }
      ]
      output_map [
      {
        key: "input_ids"
        value: "input_ids"
      },
      {
        key: "attention_mask"
        value: "attention_mask"
      },
      {
        key: "position_ids"
        value: "position_ids"
      },
      {
        key: "token_type_ids"
        value: "token_type_ids"
      },
      {
        key: "text_token_mask"
        value: "text_token_mask"
      },
      {
        key: "pos_map"
        value: "pos_map"
      },
      {
        key: "target_sizes"
        value: "target_sizes"
      }
      ]
    },
    {
      model_name: "gdino_trt"
      model_version: -1
      input_map [
      {
	key: "inputs"
	value: "inputs"
      },
      {
        key: "input_ids"
        value: "input_ids"
      },
      {
        key: "attention_mask"
        value: "attention_mask"
      },
      {
        key: "position_ids"
        value: "position_ids"
      },
      {
        key: "token_type_ids"
        value: "token_type_ids"
      },
      {
        key: "text_token_mask"
        value: "text_token_mask"
      }
    ]
    output_map [
      {
        key: "pred_logits"
        value: "pred_logits"
      },
      {
        key: "pred_boxes"
        value: "pred_boxes"
      }
    ]
    }
    ,
    {
     model_name: "gdino_postprocess"
      model_version: -1
      input_map [
      {
        key: "pred_logits"
        value: "pred_logits"
      },
      {
        key: "pred_boxes"
        value: "pred_boxes"
      }
      ,
      {
        key: "pos_map"
        value: "pos_map"
      },
      {
        key: "target_sizes"
        value: "target_sizes"
      }
      ]
      output_map [
      {
        key: "labels"
        value: "labels"
      },
      {
        key: "boxes"
        value: "boxes"
      }
      ,
      {
        key: "scores"
        value: "scores"
      }
      ]
    }
  ]
}
