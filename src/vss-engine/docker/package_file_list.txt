######################################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
######################################################################################################
__init__.py
asset_manager.py
base_class.py
chunk_info.py
client/assets/app_bar.html
client/assets/chatbot-icon-60px.png
client/assets/kaizen-theme.css
client/assets/kaizen-theme.json
client/assets/user-icon-60px.png
client/rtsp_stream.py
client/summarization.py
client/ui_utils.py
file_splitter.py
models/common/frame_jpeg_tensor_generator.py
models/common/model_context_frame_input.py
models/custom/custom_model.py
models/custom/samples/README.md
models/custom/samples/fuyu8b/inference.py
models/custom/samples/fuyu8b/manifest.yaml
models/custom/samples/neva/inference.py
models/custom/samples/neva/manifest.yaml
models/openai_compat/openai_compat_model.py
models/openai_compat/internal/util.py
models/nvila/VILA/llava/model/apply_delta.py
models/nvila/VILA/llava/model/configuration_llava.py
models/nvila/VILA/llava/model/qlinear_te.py
models/nvila/VILA/llava/model/builder.py
models/nvila/VILA/llava/model/encoders/image/__init__.py
models/nvila/VILA/llava/model/encoders/image/basic.py
models/nvila/VILA/llava/model/encoders/__init__.py
models/nvila/VILA/llava/model/encoders/video/__init__.py
models/nvila/VILA/llava/model/encoders/video/basic.py
models/nvila/VILA/llava/model/encoders/video/tsp.py
models/nvila/VILA/llava/model/encoders/base.py
models/nvila/VILA/llava/model/llava_arch.py
models/nvila/VILA/llava/model/__init__.py
models/nvila/VILA/llava/model/multimodal_projector/base_projector.py
models/nvila/VILA/llava/model/multimodal_projector/builder.py
models/nvila/VILA/llava/model/quantization/QAdd.py
models/nvila/VILA/llava/model/quantization/QGELU.py
models/nvila/VILA/llava/model/quantization/__init__.py
models/nvila/VILA/llava/model/quantization/Qconfig.py
models/nvila/VILA/llava/model/quantization/QIdentity.py
models/nvila/VILA/llava/model/quantization/FloatPointQuantizeTorch.py
models/nvila/VILA/llava/model/quantization/utils.py
models/nvila/VILA/llava/model/quantization/FloatPointQuantizeTriton.py
models/nvila/VILA/llava/model/quantization/debug.txt
models/nvila/VILA/llava/model/quantization/QAct.py
models/nvila/VILA/llava/model/quantization/QLayerNorm.py
models/nvila/VILA/llava/model/quantization/QLinear.py
models/nvila/VILA/llava/model/quantization/QMul.py
models/nvila/VILA/llava/model/quantization/QFunction.py
models/nvila/VILA/llava/model/language_model/builder.py
models/nvila/VILA/llava/model/language_model/llava_llama.py
models/nvila/VILA/llava/model/language_model/chat_templates/mistral.jinja
models/nvila/VILA/llava/model/language_model/chat_templates/qwen2.jinja
models/nvila/VILA/llava/model/language_model/qllama.py
models/nvila/VILA/llava/model/language_model/qmemllama.py
models/nvila/VILA/llava/model/language_model/qllava_qllama.py
models/nvila/VILA/llava/model/make_delta.py
models/nvila/VILA/llava/model/qfunction.py
models/nvila/VILA/llava/model/multimodal_encoder/radio_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/builder.py
models/nvila/VILA/llava/model/multimodal_encoder/siglip/modeling_siglip.py
models/nvila/VILA/llava/model/multimodal_encoder/siglip/__init__.py
models/nvila/VILA/llava/model/multimodal_encoder/vision_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/intern_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/visualize_features.py
models/nvila/VILA/llava/model/multimodal_encoder/radio_torchhub_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/clip_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/image_processor.py
models/nvila/VILA/llava/model/multimodal_encoder/siglip_encoder.py
models/nvila/VILA/llava/model/multimodal_encoder/intern/flash_attention.py
models/nvila/VILA/llava/model/multimodal_encoder/intern/modeling_intern_vit.py
models/nvila/VILA/llava/model/multimodal_encoder/intern/configuration_intern_vit.py
models/nvila/VILA/llava/model/FloatPointQuantizeTorch.py
models/nvila/VILA/llava/model/qutils.py
models/nvila/VILA/llava/model/loss.py
models/nvila/VILA/llava/model/realquantize/division_transpose.py
models/nvila/VILA/llava/model/realquantize/common.py
models/nvila/VILA/llava/model/realquantize/linear.py
models/nvila/VILA/llava/model/realquantize/quantize_and_transpose.py
models/nvila/VILA/llava/model/realquantize/division.py
models/nvila/VILA/llava/model/utils/__init__.py
models/nvila/VILA/llava/model/utils/utils.py
models/nvila/VILA/llava/model/utils/packing.py
models/nvila/VILA/llava/model/FloatPointQuantizeTriton.py
models/nvila/VILA/llava/model/consolidate.py
models/nvila/VILA/llava/train/sequence_parallel/hybrid_attn.py
models/nvila/VILA/llava/train/sequence_parallel/input_utils.py
models/nvila/VILA/llava/train/sequence_parallel/__init__.py
models/nvila/VILA/llava/train/sequence_parallel/ulysses_attn.py
models/nvila/VILA/llava/train/sequence_parallel/monkey_patch.py
models/nvila/VILA/llava/train/sequence_parallel/ring/zigzag_ring_flash_attn_varlen.py
models/nvila/VILA/llava/train/sequence_parallel/ring/triton_utils.py
models/nvila/VILA/llava/train/sequence_parallel/ring/stripe_flash_attn.py
models/nvila/VILA/llava/train/sequence_parallel/ring/__init__.py
models/nvila/VILA/llava/train/sequence_parallel/ring/zigzag_ring_flash_attn.py
models/nvila/VILA/llava/train/sequence_parallel/ring/utils.py
models/nvila/VILA/llava/train/sequence_parallel/ring/ring_flash_attn.py
models/nvila/VILA/llava/train/sequence_parallel/ring/ring_flash_attn_varlen.py
models/nvila/VILA/llava/train/sequence_parallel/all_to_all.py
models/nvila/VILA/llava/train/sequence_parallel/globals.py
models/nvila/VILA/llava/train/__init__.py
models/nvila/VILA/llava/train/deepspeed_replace/runtime/zero/mics.py
models/nvila/VILA/llava/train/callbacks/autoresume_callback.py
models/nvila/VILA/llava/train/utils.py
models/nvila/VILA/llava/conversation.py
models/nvila/VILA/llava/media.py
models/nvila/VILA/llava/entry.py
models/nvila/VILA/llava/__init__.py
models/nvila/VILA/llava/mm_utils.py
models/nvila/VILA/llava/utils/tokenizer.py
models/nvila/VILA/llava/utils/media.py
models/nvila/VILA/llava/utils/logging.py
models/nvila/VILA/llava/utils/distributed.py
models/nvila/VILA/llava/utils/__init__.py
models/nvila/VILA/llava/utils/merge_lora_weights_and_save_hf_model.py
models/nvila/VILA/llava/utils/utils.py
models/nvila/VILA/llava/utils/io.py
models/nvila/VILA/llava/constants.py
models/nvila/__init__.py
models/nvila/nvila_model.py
models/nvila/nvila_context.py
models/vila15/VILA/llava/__init__.py
models/vila15/VILA/llava/constants.py
models/vila15/VILA/llava/conversation.py
models/vila15/VILA/llava/entry.py
models/vila15/VILA/llava/mm_utils.py
models/vila15/VILA/llava/modals.py
models/vila15/VILA/llava/model/__init__.py
models/vila15/VILA/llava/model/apply_delta.py
models/vila15/VILA/llava/model/builder.py
models/vila15/VILA/llava/model/configuration_llava.py
models/vila15/VILA/llava/model/language_model/builder.py
models/vila15/VILA/llava/model/language_model/llava_gemma.py
models/vila15/VILA/llava/model/language_model/llava_llama.py
models/vila15/VILA/llava/model/language_model/llava_mistral.py
models/vila15/VILA/llava/model/language_model/llava_mixtral.py
models/vila15/VILA/llava/model/language_model/llava_mpt.py
models/vila15/VILA/llava/model/language_model/modeling_mixtral_long_context.py
models/vila15/VILA/llava/model/language_model/mpt/adapt_tokenizer.py
models/vila15/VILA/llava/model/language_model/mpt/attention.py
models/vila15/VILA/llava/model/language_model/mpt/blocks.py
models/vila15/VILA/llava/model/language_model/mpt/configuration_mpt.py
models/vila15/VILA/llava/model/language_model/mpt/custom_embedding.py
models/vila15/VILA/llava/model/language_model/mpt/flash_attn_triton.py
models/vila15/VILA/llava/model/language_model/mpt/hf_prefixlm_converter.py
models/vila15/VILA/llava/model/language_model/mpt/meta_init_context.py
models/vila15/VILA/llava/model/language_model/mpt/modeling_mpt.py
models/vila15/VILA/llava/model/language_model/mpt/norm.py
models/vila15/VILA/llava/model/language_model/mpt/param_init_fns.py
models/vila15/VILA/llava/model/llava_arch.py
models/vila15/VILA/llava/model/multimodal_encoder/builder.py
models/vila15/VILA/llava/model/multimodal_encoder/clip_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/image_processor.py
models/vila15/VILA/llava/model/multimodal_encoder/intern/configuration_intern_vit.py
models/vila15/VILA/llava/model/multimodal_encoder/intern/flash_attention.py
models/vila15/VILA/llava/model/multimodal_encoder/intern/modeling_intern_vit.py
models/vila15/VILA/llava/model/multimodal_encoder/intern_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/radio_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/radio_torchhub_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/__init__.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/configuration_siglip.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/convert_siglip_to_hf.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/image_processing_siglip.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/modeling_siglip.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/processing_siglip.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip/tokenization_siglip.py
models/vila15/VILA/llava/model/multimodal_encoder/siglip_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/vision_encoder.py
models/vila15/VILA/llava/model/multimodal_encoder/visualize_features.py
models/vila15/VILA/llava/model/multimodal_projector/base_projector.py
models/vila15/VILA/llava/model/multimodal_projector/builder.py
models/vila15/VILA/llava/model/utils.py
models/vila15/VILA/llava/train/__init__.py
models/vila15/VILA/llava/train/sequence_parallel/__init__.py
models/vila15/VILA/llava/train/sequence_parallel/all_to_all.py
models/vila15/VILA/llava/train/sequence_parallel/globals.py
models/vila15/VILA/llava/train/sequence_parallel/hybrid_attn.py
models/vila15/VILA/llava/train/sequence_parallel/input_utils.py
models/vila15/VILA/llava/train/sequence_parallel/monkey_patch.py
models/vila15/VILA/llava/train/sequence_parallel/ring/__init__.py
models/vila15/VILA/llava/train/sequence_parallel/ring/ring_flash_attn.py
models/vila15/VILA/llava/train/sequence_parallel/ring/ring_flash_attn_varlen.py
models/vila15/VILA/llava/train/sequence_parallel/ring/stripe_flash_attn.py
models/vila15/VILA/llava/train/sequence_parallel/ring/triton_utils.py
models/vila15/VILA/llava/train/sequence_parallel/ring/utils.py
models/vila15/VILA/llava/train/sequence_parallel/ring/zigzag_ring_flash_attn.py
models/vila15/VILA/llava/train/sequence_parallel/ring/zigzag_ring_flash_attn_varlen.py
models/vila15/VILA/llava/train/sequence_parallel/ulysses_attn.py
models/vila15/VILA/llava/train/utils.py
models/vila15/VILA/llava/unit_test_utils.py
models/vila15/VILA/llava/utils.py
models/vila15/VILA/llava/utils/__init__.py
models/vila15/VILA/llava/utils/distributed.py
models/vila15/VILA/llava/utils/merge_lora_weights_and_save_hf_model.py
models/vila15/VILA/llava/utils/tokenizer.py
models/vila15/VILA/llava/utils/utils.py
models/vila15/__init__.py
models/vila15/trt_helper/build_engine.sh
models/vila15/trt_helper/build_visual_engine.py
models/vila15/trt_helper/convert_checkpoint.py
models/vila15/trt_helper/hf_lora_convert.py
models/vila15/trt_helper/quantize.py
models/vila15/vila15_context.py
models/vila15/vila15_embedding_generator.py
models/vila15/vila15_model.py
models/cosmos_reason1/__init__.py
models/cosmos_reason1/cosmos_reason1_context.py
models/cosmos_reason1/cosmos_reason1_model.py
otel_helper.py
utils.py
via_client_cli.py
via_demo_client.py
via_exception.py
via_health_eval.py
via_logger.py
via_server.py
via_stream_handler.py
vss_api_models.py
vlm_pipeline/__init__.py
vlm_pipeline/embedding_helper.py
vlm_pipeline/ngc_model_downloader.py
vlm_pipeline/process_base.py
vlm_pipeline/vlm_pipeline.py
vlm_pipeline/video_file_frame_getter.py
cv_pipeline/__init__.py
cv_pipeline/cv_metadata_fuser.py
cv_pipeline/cv_pipeline.py
cv_pipeline/FPS.py
cv_pipeline/gsam_model_trt.py
cv_pipeline/gsam_pipeline_trt_ds.py
cv_pipeline/update_gdino_model.py
trt_inference/__init__.py
trt_inference/data_loader.py
trt_inference/gdino_inferencer.py
trt_inference/trt_inferencer.py
trt_inference/utils.py
#
