{"name": "gradio_videotimeline", "version": "0.14.13", "description": "Gradio UI packages", "type": "module", "author": "", "license": "ISC", "private": false, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.7", "@ffmpeg/util": "^0.12.1", "@gradio/atoms": "0.16.0", "@gradio/client": "1.14.2", "@gradio/icons": "0.12.0", "@gradio/image": "0.22.5", "@gradio/statustracker": "0.10.10", "@gradio/upload": "0.16.4", "@gradio/utils": "0.10.2", "@gradio/wasm": "0.18.1", "hls.js": "^1.5.13", "mrmime": "^2.0.0"}, "devDependencies": {"@gradio/preview": "0.13.0", "@tsconfig/svelte": "^5.0.4", "svelte-preprocess": "^6.0.3", "typescript": "^5.8.3", "vite-plugin-commonjs": "^0.10.4"}, "exports": {"./package.json": "./package.json", ".": {"gradio": "./index.ts", "svelte": "./dist/index.js", "types": "./dist/index.d.ts"}, "./example": {"gradio": "./Example.svelte", "svelte": "./dist/Example.svelte", "types": "./dist/Example.svelte.d.ts"}, "./shared": {"gradio": "./shared/index.ts", "svelte": "./dist/shared/index.js", "types": "./dist/shared/index.d.ts"}, "./base": {"gradio": "./shared/VideoPreview.svelte", "svelte": "./dist/shared/VideoPreview.svelte", "types": "./dist/shared/VideoPreview.svelte.d.ts"}}, "peerDependencies": {"svelte": "^4.0.0"}, "main": "index.ts", "main_changeset": true, "repository": {"type": "git", "url": "git+https://github.com/gradio-app/gradio.git", "directory": "js/video"}}