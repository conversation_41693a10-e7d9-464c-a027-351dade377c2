# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: LicenseRef-NvidiaProprietary
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.

[build-system]
requires = [
  "hatchling",
  "hatch-requirements-txt",
  "hatch-fancy-pypi-readme>=22.5.0",
]
build-backend = "hatchling.build"

[project]
name = "gradio_videotimeline"
version = "1.0.2"
description = "Video component to show event summary"
readme = "README.md"
license = "apache-2.0"
requires-python = ">=3.10"
authors = [{ name = "An<PERSON>", email = "<EMAIL>" }]
keywords = ["gradio-custom-component", "gradio-template-Video", "Video Event Summary", "Video Timeline"]
# Add dependencies here
dependencies = ["gradio>=4.0,<6.0"]
classifiers = [
  'Development Status :: 3 - Alpha',
  'Operating System :: OS Independent',
  'Programming Language :: Python :: 3',
  'Programming Language :: Python :: 3 :: Only',
  'Programming Language :: Python :: 3.8',
  'Programming Language :: Python :: 3.9',
  'Programming Language :: Python :: 3.10',
  'Programming Language :: Python :: 3.11',
  'Topic :: Scientific/Engineering',
  'Topic :: Scientific/Engineering :: Artificial Intelligence',
  'Topic :: Scientific/Engineering :: Visualization',
]

# The repository and space URLs are optional, but recommended.
# Adding a repository URL will create a badge in the auto-generated README that links to the repository.
# Adding a space URL will create a badge in the auto-generated README that links to the space.
# This will make it easy for people to find your deployed demo or source code when they
# encounter your project in the wild.

# [project.urls]
# repository = "your github repository"
# space = "your space url"

[project.optional-dependencies]
dev = ["build", "twine"]

[tool.hatch.build]
artifacts = ["/backend/gradio_videotimeline/templates", "*.pyi"]

[tool.hatch.build.targets.wheel]
packages = ["/backend/gradio_videotimeline"]
